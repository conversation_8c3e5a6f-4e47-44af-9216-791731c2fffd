﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using FluentValidation;
using FluentValidation.Results;

namespace Aveva.Platform.Catalog.Domain.Validation;

/// <summary>
/// BaseClass for ServiceEntry validations.
/// </summary>
/// <typeparam name="T">ServiceEntry custom resource.</typeparam>
public abstract class BaseCatalogValidator<T> : AbstractValidator<T>
    where T : V1ServiceEntry
{
    /// <summary>
    /// Current Service entries available in k8s Cluster.
    /// </summary>
    protected ServiceCollectionResponse? ExistingEntries { get; set; }

    /// <summary>
    /// Should existing entries be considered when performing validation.
    /// </summary>
    protected bool SkipExistingEntriesValidation { get; set; }

    /// <summary>
    /// Validates Lifecycle.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected static IEnumerable<ValidationFailure> ValidateLifecycle(T catalogData)
    {
        var result = Enumerable.Empty<ValidationFailure>();

        if (catalogData == null)
        {
            return result;
        }

        if (catalogData.Lifecycle == null)
        {
            result = result.Append(new ValidationFailure(
                     nameof(V1ServiceEntry.Lifecycle),
                     $"{nameof(V1ServiceEntry.Lifecycle)} is required",
                     catalogData.Lifecycle));
        }
        else if (catalogData.Lifecycle.Trigger != V1Trigger.Catalog && catalogData.Availability != null)
        {
            result = result.Append(new ValidationFailure(
                nameof(V1ServiceEntry.Availability),
                "ServiceEntry Availability cannot be set if the lifecycle trigger is not catalog."));
        }

        return result;
    }

    /// <summary>
    /// Validates Lifecycle.InstanceMode.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected static IEnumerable<ValidationFailure> ValidateInstanceMode(T catalogData)
    {
        var result = Enumerable.Empty<ValidationFailure>();

        if (catalogData == null)
        {
            return result;
        }

        if (catalogData.Lifecycle != null)
        {
            if (catalogData.Lifecycle.Trigger != V1Trigger.None)
            {
                if (!catalogData.Lifecycle.InstanceMode.HasValue)
                {
                    result = result.Append(new ValidationFailure(
                        nameof(V1ServiceEntry.Lifecycle.InstanceMode),
                        $"InstanceMode is required for {catalogData.Lifecycle.Trigger} trigger.",
                        catalogData.Lifecycle.InstanceMode));
                }
            }
        }

        return result;
    }

    /// <summary>
    /// Validates legacy lifecycle protocol.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected static List<ValidationFailure> ValidateLegacyProtocol(T catalogData)
    {
        var results = new List<ValidationFailure>();
        if (catalogData == null)
        {
            return results;
        }

        if (catalogData.Lifecycle == null || catalogData.Lifecycle.Protocol != V1IntegrationProtocol.Legacy)
        {
            return results;
        }

        if (catalogData.Lifecycle.ProtocolOptions == null)
        {
            results.Add(new ValidationFailure(
                nameof(catalogData.Lifecycle.ProtocolOptions),
                "ProtocolOptions required for the Legacy Protocol"));
        }
        else
        {
            var legacyOptions = catalogData.Lifecycle.ProtocolOptions;
            if (legacyOptions == null)
            {
                results.Add(new ValidationFailure(
                    nameof(catalogData.Lifecycle.ProtocolOptions),
                    "ProtocolOptions required for Legacy Protocol"));
            }
            else
            {
                if (string.IsNullOrWhiteSpace(legacyOptions.SolutionDefinition))
                {
                    results.Add(new ValidationFailure(
                        nameof(V1ProtocolOptions.SolutionDefinition),
                        "SolutionDefinition required for the Legacy Protocol"));
                }

                if (legacyOptions.Mappings == null)
                {
                    results.Add(new ValidationFailure(
                        nameof(V1ProtocolOptions.Mappings),
                        "Mappings required for the Legacy Protocol"));
                }
                else
                {
                    if (legacyOptions.Mappings.Geographies == null)
                    {
                        results.Add(new ValidationFailure(
                            nameof(V1ProtocolOptions.Mappings.Geographies),
                            "You must have geographies and at least a 'default' geography mapping"));
                    }
                    else
                    {
                        if (!legacyOptions.Mappings.Geographies.ContainsKey("default"))
                        {
                            results.Add(new ValidationFailure(
                                nameof(V1ProtocolOptions.Mappings.Geographies),
                                "You must have at least a 'default' geography mapping"));
                        }
                    }

                    if (legacyOptions.Mappings.Dependencies != null)
                    {
                        foreach (var dependency in legacyOptions.Mappings.Dependencies)
                        {
                            if (string.IsNullOrEmpty(dependency.Value.IntegrationDefinition))
                            {
                                results.Add(new ValidationFailure(
                                    nameof(dependency.Value.IntegrationDefinition),
                                    $"Integration definition is required for dependency '{dependency.Key}'"));
                            }

                            ValidateContextConfig(
                                dependency.Key,
                                dependency.Value.SourceContextConfig,
                                "Source",
                                catalogData.Dependencies?.GetValueOrDefault(dependency.Key)?.Config,
                                results);

                            ValidateContextConfig(
                                dependency.Key,
                                dependency.Value.TargetContextConfig,
                                "Target",
                                catalogData.Dependencies?.GetValueOrDefault(dependency.Key)?.Config,
                                results);
                        }
                    }
                }
            }
        }

        return results;
    }

    /// <summary>
    /// Validates webhook lifecycle protocol.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected static List<ValidationFailure> ValidateWebhookProtocol(T catalogData)
    {
        var results = new List<ValidationFailure>();
        if (catalogData == null)
        {
            return results;
        }

        if (catalogData.Lifecycle == null || catalogData.Lifecycle.Protocol != V1IntegrationProtocol.Webhook)
        {
            return results;
        }

        if (catalogData.Lifecycle.ProtocolOptions == null)
        {
            results.Add(new ValidationFailure(
                nameof(catalogData.Lifecycle.ProtocolOptions),
                "ProtocolOptions required for the Webhook Protocol"));
        }
        else
        {
            var webhookOptions = catalogData.Lifecycle.ProtocolOptions;
            if (webhookOptions.WebhookUri == null)
            {
                results.Add(new ValidationFailure(
                    nameof(V1ProtocolOptions.WebhookUri),
                    "WebhookUri required for the Webhook Protocol"));
            }
            else if (!webhookOptions.WebhookUri.IsAbsoluteUri)
            {
                results.Add(new ValidationFailure(
                    nameof(V1ProtocolOptions.WebhookUri),
                    "WebhookUri must be a valid absolute URI"));
            }
        }

        return results;
    }

    /// <summary>
    /// Validates ExternalIdentities.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected static IEnumerable<ValidationFailure> ValidateExternalIdentities(T catalogData)
    {
        var result = Enumerable.Empty<ValidationFailure>();

        if (catalogData == null || catalogData.ExternalIdentities == null)
        {
            return result;
        }

        foreach (V1ExternalIdentity identity in catalogData.ExternalIdentities)
        {
            if (identity.Scopes.Count == 0)
            {
                result = result.Append(new ValidationFailure(
                    nameof(V1ExternalIdentity.Scopes),
                    $"{nameof(V1ExternalIdentity.Scopes)} cannot be empty for '{identity.Id}' identity."));
            }

            if (identity.Scopes.Count != identity.Scopes.Distinct().Count())
            {
                result = result.Append(new ValidationFailure(
                    nameof(V1ExternalIdentity.Scopes),
                    $"{nameof(V1ExternalIdentity.Scopes)} cannot contain duplicate scopes for '{identity.Id}' identity."));
            }
        }

        return result;
    }

    /// <summary>
    /// Rule builder to validate required properties and length.
    /// </summary>
    protected void ValidationRuleBuilder()
    {
        RuleFor(x => x.Id)
        .MaximumLength(38)
        .NotEmpty()
        .NotNull()
        .WithMessage($"{nameof(V1ServiceEntry.Id)} is required. Length should be <= 38.");

        RuleFor(x => x.DisplayName)
        .MaximumLength(100)
        .NotNull()
        .NotEmpty()
        .WithMessage($"{nameof(V1ServiceEntry.DisplayName)} is required. Length should be <= 100.");

        RuleFor(x => x.Description)
        .MaximumLength(2000)
        .WithMessage($"{nameof(V1ServiceEntry.Description)} is required. Length should be < 2000.");

        // Validation is necessary because the URL still gets constructed
        RuleFor(x => x.IconUrl)
        .Must(IsValidSource)
        .WithMessage(x => $"{nameof(V1ServiceEntry.IconUrl)} string is not valid: {x.IconUrl?.ToString()}");

        static bool IsValidSource(Uri? source)
        {
            if (source == null || string.IsNullOrEmpty(source.OriginalString) || Uri.IsWellFormedUriString(source.OriginalString, UriKind.Absolute))
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// Validate required properties for dependency.
    /// </summary>
    /// <returns>A list of validation failures.</returns>
    protected List<ValidationFailure> ValidateDependencies(T catalogData)
    {
        ArgumentNullException.ThrowIfNull(catalogData, nameof(catalogData));
        var result = new List<ValidationFailure>();

        if (catalogData.Dependencies == null)
        {
            return result;
        }

        foreach (var dependency in catalogData.Dependencies)
        {
            if (string.IsNullOrEmpty(dependency.Value.Type.ToString()))
            {
                result.Add(new ValidationFailure(nameof(V1CatalogDataDependency.Type), $"Type is required for the dependency: {dependency.Key}", dependency));
            }

            if (!Enum.TryParse<V1CatalogDataDependencyType>(dependency.Value.Type.ToString(), true, out _))
            {
                result.Add(new ValidationFailure(
                    nameof(V1CatalogDataDependency.Type),
                    $"Dependency Type {dependency.Value.Type} is invalid for {dependency.Key}.",
                    dependency.Value.Type!.ToString()));
            }

            if (string.IsNullOrEmpty(dependency.Value.Cardinality.ToString()))
            {
                result.Add(new ValidationFailure(nameof(V1CatalogDataDependency.Cardinality), $"Cardinality is required for the dependency: {dependency.Key}", dependency));
            }

            if (!Enum.TryParse<V1CatalogDataDependencyCardinality>(dependency.Value.Cardinality.ToString(), true, out _))
            {
                result.Add(new ValidationFailure(
                    nameof(V1CatalogDataDependency.Cardinality),
                    $"Dependency cardinality {dependency.Value.Cardinality} is invalid for {dependency.Key}.",
                    dependency.Value.Cardinality!.ToString()));
            }

            result.AddRange(ValidateDependencyConfiguration(dependency.Key, dependency.Value.Config));
        }

        return result;
    }

    /// <summary>
    /// Validates Dependencies.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected IEnumerable<ValidationFailure> ValidateDependenciesExist(T catalogData)
    {
#pragma warning disable CA1510 // Use ArgumentNullException throw helper
        if (catalogData == null)
        {
            throw new ArgumentNullException(nameof(catalogData));
        }
#pragma warning restore CA1510 // Use ArgumentNullException throw helper
        var result = Enumerable.Empty<ValidationFailure>();

        if (ExistingEntries != null && !SkipExistingEntriesValidation)
        {
            var dependencies = catalogData.Dependencies ?? [];

            foreach (var dependency in dependencies)
            {
                var dependantServiceEntry = ExistingEntries.Items?.FirstOrDefault(x => x.Id!.Equals(dependency.Key, StringComparison.OrdinalIgnoreCase));

                if (dependantServiceEntry == null)
                {
                    // This property name will be incorrect unless the API model property names match the domain names.
                    // This means that the error response will refer to domain model property names not the API model.
                    result = result.Append(new ValidationFailure(nameof(V1ServiceEntry.Dependencies), $"Dependant Service entry {dependency.Key} is not found.", dependency));
                }
            }
        }

        return result;
    }

    /// <summary>
    /// Validates Applications.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected IEnumerable<ValidationFailure> ValidateApplications(T catalogData)
    {
        ArgumentNullException.ThrowIfNull(catalogData, nameof(catalogData));
        var result = Enumerable.Empty<ValidationFailure>();

        if (catalogData.Applications != null && catalogData.Applications.Count > 0)
        {
            foreach (var application in catalogData.Applications)
            {
                if (application.Urls != null && application.Urls.Count > 0)
                {
                    if (!application.Urls.ContainsKey("default"))
                    {
                        return result.Append(new ValidationFailure(
                            nameof(V1Application.Urls),
                            $"Application {application.Name} urls must also contain a default entry.",
                            application));
                    }
                }
            }
        }

        return result;
    }

    /// <summary>
    /// Validates Geographies.
    /// </summary>
    /// <param name="catalogData">Created or updated ServiceEntry.</param>
    /// <returns>Validation Failures.</returns>
    protected IEnumerable<ValidationFailure> ValidateGeographies(T catalogData)
    {
        ArgumentNullException.ThrowIfNull(catalogData, nameof(catalogData));
        var result = Enumerable.Empty<ValidationFailure>();

        if (catalogData.Geographies != null && catalogData.Geographies.Count > 0)
        {
            if (catalogData.HostingType != V1HostingType.External)
            {
                result = result.Append(new ValidationFailure(
                    nameof(V1ServiceEntry.Geographies),
                    $"{nameof(V1ServiceEntry.Geographies)} only valid for HostingType '{V1HostingType.External}'."));
            }
        }
        else
        {
            if (catalogData.HostingType == V1HostingType.External)
            {
                result = result.Append(new ValidationFailure(
                    nameof(V1ServiceEntry.Geographies),
                    $"{nameof(V1ServiceEntry.Geographies)} must have at least one geography for HostingType '{V1HostingType.External}'."));
            }
        }

        return result;
    }

    /// <summary>
    /// Validate Dependency Config.
    /// </summary>
    /// <param name="dependency">Name of the dependency.</param>
    /// <param name="dependencyConfig">Created or updated DependencyConfig.</param>
    /// <returns>Validation Failures.</returns>
    private static List<ValidationFailure> ValidateDependencyConfiguration(string dependency, IDictionary<string, V1CatalogDataDependencyConfig>? dependencyConfig)
    {
        var result = new List<ValidationFailure>();

        if (dependencyConfig == null ||
            dependencyConfig.Count == 0)
        {
            return result;
        }

        foreach (var config in dependencyConfig)
        {
            if (string.IsNullOrEmpty(config.Value.Label))
            {
                result.Add(new ValidationFailure(nameof(V1CatalogDataDependencyConfig.Label), $"Required for the dependency: {dependency} config: {config.Key}"));
            }

            if (string.IsNullOrEmpty(config.Value.Help))
            {
                result.Add(new ValidationFailure(nameof(V1CatalogDataDependencyConfig.Help), $"Required for the dependency: {dependency} config: {config.Key}"));
            }

            if (config.Value.Min < 0)
            {
                result.Add(new ValidationFailure(nameof(V1CatalogDataDependencyConfig.Min), $"Must be positive for the dependency: {dependency} config: {config.Key}"));
            }

            if (config.Value.Max < config.Value.Min)
            {
                result.Add(new ValidationFailure(nameof(V1CatalogDataDependencyConfig.Max), $"Must be greater than or equal to min for the dependency: {dependency} config: {config.Key}"));
            }
        }

        return result;
    }

    /// <summary>
    /// Validates the context configuration for a dependency in the legacy protocol.
    /// Ensures that if a context is provided, it exists in the dependency's config and is marked as required.
    /// </summary>
    /// <param name="dependencyKey">The key identifying the dependency being validated.</param>
    /// <param name="contextConfig">The context configuration value to validate (e.g., source or target context).</param>
    /// <param name="contextType">The type of context being validated (e.g., "Source" or "Target").</param>
    /// <param name="depConfig">The dictionary of configuration values for the dependency.</param>
    /// <param name="results">The collection to add validation failures to if validation fails.</param>
    private static void ValidateContextConfig(
      string dependencyKey,
      string? contextConfig,
      string contextType,
      IDictionary<string, V1CatalogDataDependencyConfig>? depConfig,
      List<ValidationFailure> results)
    {
        if (contextConfig == null)
        {
            return;
        }

        if (string.IsNullOrWhiteSpace(contextConfig))
        {
            results.Add(new ValidationFailure(
                nameof(contextConfig),
                $"{contextType} context is required for dependency '{dependencyKey}'"));
            return;
        }

        if (depConfig == null || !depConfig.TryGetValue(contextConfig, out var configValue))
        {
            results.Add(new ValidationFailure(
                nameof(contextConfig),
                $"{contextType} context config '{contextConfig}' does not exist in dependency '{dependencyKey}' config"));
            return;
        }

        if (!configValue.Required)
        {
            results.Add(new ValidationFailure(
                nameof(contextConfig),
                $"{contextType} context config '{contextConfig}' in dependency '{dependencyKey}' must be marked as required"));
        }
    }
}