# [DO NOT MODIFY] Autogenerated by Aveva.Platform.Gateway.Sdk.Cli
spec:
  displayName: Catalog
  version: v2
  serviceId: catalog
  document: |-
    {
      "openapi": "3.0.4",
      "info": {
        "title": "Catalog Service",
        "description": "The Catalog service provides a comprehensive registry of available services within the CONNECT platform. It enables you to discover, explore, and understand the capabilities of services you can provision to your account.\n\nThis API gives you access to detailed information about each service, including its functionality, requirements, configuration options, and availability. You can browse the service catalog, view specific service details, and determine which services best meet your business needs.\n\nThe Catalog service works closely with Instance Management, which handles the actual provisioning of services to your account. While the Catalog provides information about available services, Instance Management is responsible for creating and managing instances of those services. Services with a lifecycle trigger of `Catalog` can be provisioned through Instance Management based on the information stored in the catalog.\n\nUse this API to explore the platform's capabilities, plan your service architecture, and make informed decisions about which services to implement in your solutions.",
        "version": "2.0"
      },
      "paths": {
        "/ops/v2/services": {
          "get": {
            "tags": [
              "CatalogV2"
            ],
            "summary": "Get catalog services",
            "description": "Gets a collection of all catalog service entries. This operations endpoint allows viewing all services regardless of their availability to specific accounts.",
            "operationId": "GetAllAccountScopedCatalogsOpsV2",
            "parameters": [
              {
                "name": "accountId",
                "in": "query",
                "description": "The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.",
                "schema": {
                  "type": "string"
                }
              },
              {
                "name": "category",
                "in": "query",
                "description": "Defines the classification categories for services in the catalog. Categories help organize services based on their primary purpose or function.",
                "schema": {
                  "$ref": "#/components/schemas/Category"
                }
              }
            ],
            "responses": {
              "200": {
                "description": "OK",
                "content": {
                  "application/json": {
                    "schema": {
                      "$ref": "#/components/schemas/ServiceCollectionResponse"
                    }
                  }
                }
              },
              "404": {
                "description": "Not Found"
              }
            },
            "x-RequestDTO": "Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2.ServiceCollectionResponse"
          }
        },
        "/ops/v2/services/{id}": {
          "get": {
            "tags": [
              "CatalogV2"
            ],
            "summary": "Get catalog service by ID",
            "description": "Gets a specific catalog service entry by its unique `id`. This operations endpoint returns detailed information about a single service, including its availability configuration for different accounts.",
            "operationId": "GetAccountScopedCatalogByIdOpsV2",
            "parameters": [
              {
                "name": "id",
                "in": "path",
                "description": "The unique identifier for this service. This ID is used to reference the service in other operations.",
                "required": true,
                "schema": {
                  "type": "string"
                }
              },
              {
                "name": "accountId",
                "in": "query",
                "description": "The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.",
                "schema": {
                  "type": "string"
                }
              }
            ],
            "responses": {
              "200": {
                "description": "OK",
                "content": {
                  "application/json": {
                    "schema": {
                      "$ref": "#/components/schemas/ServiceResponse"
                    }
                  }
                }
              },
              "404": {
                "description": "Not Found"
              }
            },
            "x-RequestDTO": "Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2.ServiceResponse"
          }
        }
      },
      "components": {
        "schemas": {
          "Application": {
            "type": "object",
            "properties": {
              "name": {
                "type": "string",
                "description": "The name of the application. The unique name that identifies this application within a service. This name is used for display and reference purposes.",
                "nullable": true
              },
              "urls": {
                "type": "object",
                "additionalProperties": {
                  "type": "string"
                },
                "description": "Optional URLs keyed to launch for specific valid geographies and/or default URLs to launch if a geography is not identified. A dictionary of URLs where keys represent geography codes and values are the corresponding application launch URLs. If any URLs are provided, a `default` key must be included to specify the URL to use when no specific geography match is found.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines service applications, where each additional property represents an application. A representation of an application that belongs to a service in the catalog, containing information like name and access URLs."
          },
          "Category": {
            "enum": [
              "Data",
              "Ingress"
            ],
            "type": "string",
            "description": "Defines the classification categories for services in the catalog. Categories help organize services based on their primary purpose or function."
          },
          "Dependency": {
            "type": "object",
            "properties": {
              "type": {
                "$ref": "#/components/schemas/DependencyType"
              },
              "cardinality": {
                "$ref": "#/components/schemas/DependencyCardinality"
              },
              "colocated": {
                "type": "boolean",
                "description": "Specifies whether the dependent service must be located in the same geography (`true`) or not (`false`).",
                "nullable": true
              },
              "related": {
                "type": "string",
                "description": "The identifier of a related dependency.",
                "nullable": true
              },
              "config": {
                "type": "object",
                "additionalProperties": {
                  "$ref": "#/components/schemas/DependencyConfig"
                },
                "description": "Defines additional configuration values for integrating dependent services. Each key represents a configuration parameter name, and the value contains details about the parameter including its type, purpose, and constraints.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines a relationship between services where one service requires functionality from another service."
          },
          "DependencyCardinality": {
            "enum": [
              "One",
              "Many"
            ],
            "type": "string",
            "description": "Specifies whether a catalog service can connect to one (`One`) or many (`Many`) instances of a dependent service."
          },
          "DependencyConfig": {
            "type": "object",
            "properties": {
              "label": {
                "type": "string",
                "description": "The user-friendly display name for the configuration setting. This label appears in user interfaces when configuring service dependencies.",
                "nullable": true
              },
              "help": {
                "type": "string",
                "description": "Additional explanatory text that provides guidance on how to use this configuration setting. This help text gives users context about what information should be provided.",
                "nullable": true
              },
              "required": {
                "type": "boolean",
                "description": "Indicates whether this configuration value must be provided (`true`) or can be omitted (`false`) when establishing the dependency between services."
              },
              "min": {
                "type": "integer",
                "description": "The minimum length constraint for the configuration value. When specified, the provided configuration must contain at least this many characters. If not set, there is no minimum length restriction.",
                "format": "int32",
                "nullable": true
              },
              "max": {
                "type": "integer",
                "description": "The maximum length constraint for the configuration value. When specified, the provided configuration must not exceed this number of characters. If not set, there is no maximum length restriction.",
                "format": "int32",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines configuration options for service dependencies. Each configuration represents a setting needed to properly integrate with a dependent service."
          },
          "DependencyType": {
            "enum": [
              "Optional",
              "Required"
            ],
            "type": "string",
            "description": "Specifies whether the dependent service must be available (`Required`) or not (`Optional`) when a catalog service is created."
          },
          "ExternalIdentity": {
            "type": "object",
            "properties": {
              "id": {
                "type": "string",
                "description": "The unique identifier for this external identity. This ID is used to reference this specific identity in other operations.",
                "nullable": true
              },
              "type": {
                "$ref": "#/components/schemas/ExternalIdentityType"
              },
              "scopes": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/ExternalIdentityScope"
                },
                "description": "The authorization scopes associated with this external identity. Each scope represents a specific permission or access level granted to the identity.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Represents an external identity that can be associated with a service. External identities provide integration points with third-party authentication and authorization systems."
          },
          "ExternalIdentityScope": {
            "enum": [
              "apiRole",
              "opsRole"
            ],
            "type": "string",
            "description": "Defines the authorization scope types available for external identities. Each scope represents a specific permission level or access role."
          },
          "ExternalIdentityType": {
            "enum": [
              "AvevaRnDEntraID"
            ],
            "type": "string",
            "description": "Defines the supported external identity providers that can be used for authentication and authorization with services."
          },
          "Geography": {
            "type": "object",
            "properties": {
              "id": {
                "type": "string",
                "description": "The geography identifier. This unique code represents a specific geographic region where a service can be deployed and accessed.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines the catalog service allowed geographies. Geographies represent regions where a service can be deployed and accessed."
          },
          "LegacyProtocolApplicationMapping": {
            "type": "object",
            "properties": {
              "name": {
                "type": "string",
                "description": "The name of the application to be mapped. This must match an application name defined in the service's application collection.",
                "nullable": true
              },
              "capabilityDefinition": {
                "type": "string",
                "description": "The name of the legacy SCM capability definition to which the application corresponds. This identifier is used by legacy systems to recognize and interact with the application.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines the mapping of an application to a legacy Solution and Capability Management (SCM) capability definition. This mapping enables backward compatibility with systems that use the older SCM protocol."
          },
          "LegacyProtocolDependencyMapping": {
            "required": [
              "integrationDefinition"
            ],
            "type": "object",
            "properties": {
              "integrationDefinition": {
                "type": "string",
                "description": "The integration definition name this corresponds to in the legacy system. This identifier matches the integration definition in the legacy SCM system that this dependency mapping represents.",
                "nullable": true
              },
              "sourceContextConfig": {
                "type": "string",
                "description": "The identifier of the configuration parameter whose value should be provided as the `sourceContext` in the legacy protocol. This specifies which configuration value from the dependency should be used as the source context in legacy integrations.",
                "nullable": true
              },
              "targetContextConfig": {
                "type": "string",
                "description": "The identifier of the configuration parameter whose value should be provided as the `targetContext` in the legacy protocol. This specifies which configuration value from the dependency should be used as the target context in legacy integrations.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines the mapping for a dependency in the legacy protocol. This mapping connects modern service dependencies to the legacy Solution and Capability Management (SCM) integration system."
          },
          "LegacyProtocolMappings": {
            "type": "object",
            "properties": {
              "geographies": {
                "type": "object",
                "additionalProperties": {
                  "type": "string"
                },
                "description": "The mapping of geographies to the legacy declared SCM solution regions. Keys represent geography IDs in the current system, and values represent the corresponding region identifiers in the legacy SCM system.",
                "nullable": true
              },
              "dependencies": {
                "type": "object",
                "additionalProperties": {
                  "$ref": "#/components/schemas/LegacyProtocolDependencyMapping"
                },
                "description": "The mapping of the service dependencies to legacy SCM integration definitions. Keys represent dependency identifiers in the current system, and values contain the mapping details for connecting to the legacy SCM integration system.",
                "nullable": true
              },
              "applications": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/LegacyProtocolApplicationMapping"
                },
                "description": "The mappings of applications to SCM capability definitions. Each entry defines how an application in the current system relates to a capability definition in the legacy SCM system.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines the mapping between a service and corresponding legacy Solution and Capability Management (SCM) solution definition. This mapping enables backward compatibility with systems that use the older SCM protocol."
          },
          "LegacyProtocolOptions": {
            "type": "object",
            "properties": {
              "solutionDefinition": {
                "type": "string",
                "description": "The legacy SCM solution definition to which this service corresponds. This identifier links the catalog service to its equivalent representation in the legacy SCM system.",
                "nullable": true
              },
              "mappings": {
                "$ref": "#/components/schemas/LegacyProtocolMappings"
              }
            },
            "additionalProperties": false,
            "description": "Defines how the legacy Solution and Capability Management (SCM) protocol options correspond to a catalog service. These options enable backward compatibility with systems that use the older SCM protocol."
          },
          "Lifecycle": {
            "type": "object",
            "properties": {
              "trigger": {
                "type": "string",
                "description": "Determines when and how service instances are provisioned. Valid values include:\r\n            `None` (service does not require provisioning),\r\n            `Account` (automatically provisioned when an account is created),\r\n            `Catalog` (provisioned on-demand by customer request).",
                "nullable": true
              },
              "protocol": {
                "type": "string",
                "description": "Specifies the communication pattern used for lifecycle events. The default value is `IntegrationEvent`, which implements a publish-subscribe pattern where instance management publishes lifecycle events and waits for acknowledgment from the service provider.",
                "nullable": true
              },
              "providerId": {
                "type": "string",
                "description": "Identifies the service provider responsible for handling lifecycle events. The provider implements the service-specific logic needed to create, configure, and manage service instances according to customer requirements.",
                "nullable": true
              },
              "instanceMode": {
                "type": "string",
                "description": "Defines the resource allocation strategy for service instances. Options include:\r\n            `Shared` (multiple customers share the same underlying resources with logical separation),\r\n            `Isolated` (dedicated resources are provisioned specifically for each customer instance).",
                "nullable": true
              },
              "protocolOptions": {
                "$ref": "#/components/schemas/ProtocolOptions"
              },
              "fulfillmentRequired": {
                "type": "boolean",
                "description": "Indicates whether the service requires manual input from AVEVA operators."
              }
            },
            "additionalProperties": false,
            "description": "Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns."
          },
          "ProtocolOptions": {
            "type": "object",
            "oneOf": [
              {
                "$ref": "#/components/schemas/LegacyProtocolOptions"
              },
              {
                "$ref": "#/components/schemas/WebhookProtocolOptions"
              }
            ],
            "description": "The protocol options. The actual type is determined by the Protocol property in the parent Lifecycle object. When Protocol is `Legacy`, this will be a LegacyProtocolOptions. When Protocol is `Webhook`, this will be a WebhookProtocolOptions."
          },
          "ServiceAvailability": {
            "type": "object",
            "properties": {
              "enabled": {
                "type": "boolean",
                "description": "Indicates whether the service is visible in the service catalog (`true`) or hidden (`false`).\r\n            When set to `true`, the service appears in catalog listings and can be discovered by users.\r\n            When set to `false`, the service is hidden from catalog views but may still be available through direct access.",
                "nullable": true
              },
              "visible": {
                "type": "boolean",
                "description": "Indicates whether an approval workflow is required (`true`) or if the service can be provisioned immediately (`false`).\r\n            When set to `true`, service provisioning must go through a request and approval process.\r\n            When set to `false`, accounts can provision the service immediately without approval.",
                "nullable": true,
                "deprecated": true
              },
              "limit": {
                "type": "integer",
                "description": "Specifies the maximum number of instances an account can have of this service.\r\n            A value of `null` indicates the default limit (10) is applied.\r\n            A value of `0` indicates the service cannot be provisioned.\r\n            Any positive integer represents the maximum number of instances allowed.",
                "format": "int32",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines provisioning availability and constraints for a service. This resource contains information about how a service can be discovered and provisioned to an account."
          },
          "ServiceCollectionResponse": {
            "type": "object",
            "properties": {
              "items": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/ServiceResponse"
                },
                "description": "The list of catalog services with their detailed information and configurations. Each item contains complete service details including identity, dependencies, availability settings, and lifecycle configuration.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Represents a collection of service catalog entries returned by the API."
          },
          "ServiceResponse": {
            "type": "object",
            "properties": {
              "id": {
                "type": "string",
                "description": "The unique identifier for this service. This ID is used to reference the service in other operations.",
                "nullable": true
              },
              "displayName": {
                "type": "string",
                "description": "The user-friendly name of the service displayed in user interfaces. This is the primary label used to identify the service to end users.",
                "nullable": true
              },
              "iconUrl": {
                "type": "string",
                "description": "A URL pointing to the service's icon image stored in the AVEVA CDN. When using this URL, implement proper cache control to ensure users see updated icons when they change. Browsers should be configured to periodically check for updates rather than caching indefinitely.",
                "format": "uri",
                "nullable": true
              },
              "description": {
                "type": "string",
                "description": "A detailed description of the service explaining its purpose, capabilities, and key features. This text helps users understand what the service does and when to use it.",
                "nullable": true
              },
              "dependencies": {
                "type": "object",
                "additionalProperties": {
                  "$ref": "#/components/schemas/Dependency"
                },
                "description": "Other services that this service depends on to function properly. Keys represent the dependency identifier, and values contain details about the dependency relationship including cardinality and configuration requirements.",
                "nullable": true
              },
              "applications": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/Application"
                },
                "description": "Applications associated with this service. Each application represents a discrete component or interface that provides specific functionality within the service.",
                "nullable": true
              },
              "geographies": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/Geography"
                },
                "description": "Geographic regions where this service can be provisioned. Only applicable and required for services with `External` hosting type. Each geography represents a distinct region where the service can be deployed and accessed. For non-external hosting types, this collection should be empty.",
                "nullable": true
              },
              "category": {
                "$ref": "#/components/schemas/Category"
              },
              "hostingType": {
                "type": "string",
                "description": "The deployment scope of the service. Valid values include:\r\n            `Environment` (available in all regions and geographies),\r\n            `Geography` (available in all regions within a specific geography),\r\n            `Regional` (available in a specific region within a specific geography),\r\n            `External` (hosted outside the platform but accessible through platform integration).",
                "nullable": true
              },
              "tags": {
                "type": "array",
                "items": {
                  "type": "string"
                },
                "description": "Terms used to categorize and filter the service. Tags provide additional classification beyond the primary category and help users discover related services.",
                "nullable": true
              },
              "lifecycle": {
                "$ref": "#/components/schemas/Lifecycle"
              },
              "availability": {
                "$ref": "#/components/schemas/ServiceAvailability"
              },
              "externalIdentities": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/ExternalIdentity"
                },
                "description": "External identity providers associated with this service. These identities enable integration with third-party authentication and authorization systems.",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Represents a catalog service entry with detailed information about the service and its capabilities."
          },
          "WebhookProtocolOptions": {
            "type": "object",
            "properties": {
              "webhookUri": {
                "type": "string",
                "description": "The webhook URI for webhook protocol communication.",
                "format": "uri",
                "nullable": true
              }
            },
            "additionalProperties": false,
            "description": "Defines the webhook protocol options for a catalog service."
          }
        }
      },
      "tags": [
        {
          "name": "CatalogV2",
          "description": "Discover and retrieve catalog service entries. These endpoints provide access to service metadata and availability information, allowing you to explore which services can be provisioned to your account."
        }
      ]
    }
  scope: Ops
  apiType: Service
metadata:
  name: catalog-ops-service-v2
  namespace: '{{ .Release.Namespace }}'
apiVersion: gateway.aveva.com/v1
kind: OpenApiDocument