// <auto-generated/>
using System.Runtime.Serialization;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public enum CategoryNullable
    #pragma warning restore CS1591
    {
        [EnumMember(Value = "Data")]
        #pragma warning disable CS1591
        Data,
        #pragma warning restore CS1591
        [EnumMember(Value = "Ingress")]
        #pragma warning disable CS1591
        Ingress,
        #pragma warning restore CS1591
    }
}
