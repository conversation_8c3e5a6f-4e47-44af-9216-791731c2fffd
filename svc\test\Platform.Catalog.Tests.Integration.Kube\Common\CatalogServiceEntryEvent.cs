﻿namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common
{
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    public class CatalogServiceEntryEvent
    {
        public string Id { get; set; }
        public string Geography { get; set; }
        public string Region { get; set; }
        public string CreationDate { get; set; }
        public string EventId { get; set; }
    }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
}