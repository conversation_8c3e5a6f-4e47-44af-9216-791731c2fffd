﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

/// <summary>
/// Defines a relationship between services where one service requires functionality from another service.
/// </summary>
public class Dependency
{
    /// <summary>
    /// Initializes a new instance of the <see cref="Dependency"/> class.
    /// </summary>
    public Dependency() // Required by EF
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="Dependency"/> class.
    /// </summary>
    public Dependency(DependencyType type, DependencyCardinality cardinality, bool colocated = false, Dictionary<string, DependencyConfig>? config = null)
    {
        Type = type;
        Cardinality = cardinality;
        Colocated = colocated;
        Config = config;
    }

    /// <summary>
    /// Specifies whether the dependent service must be available (`Required`) or not (`Optional`) when a catalog service is created.
    /// </summary>
    public DependencyType Type { get; set; }

    /// <summary>
    /// Specifies whether a catalog service can connect to one (`One`) or many (`Many`) instances of a dependent service.
    /// </summary>
    public DependencyCardinality Cardinality { get; set; }

    /// <summary>
    /// Specifies whether the dependent service must be located in the same geography (`true`) or not (`false`).
    /// </summary>
    public bool Colocated { get; set; }

    /// <summary>
    /// Defines additional configuration values for integrating dependent services. Each key represents a configuration parameter name, and the value contains details about the parameter including its type, purpose, and constraints.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public Dictionary<string, DependencyConfig>? Config { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        return obj is Dependency item && string.Equals(item.Type.ToString(), Type.ToString(), StringComparison.InvariantCultureIgnoreCase)
                 && string.Equals(item.Cardinality.ToString(), Cardinality.ToString(), StringComparison.InvariantCultureIgnoreCase)
                 && item.Colocated == Colocated
                 && ConfigsAreEqual(item.Config);
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
#pragma warning restore CS8618

    private bool ConfigsAreEqual(IDictionary<string, DependencyConfig>? config)
    {
        if ((config == null || config.Count == 0) && (Config == null || Config.Count == 0))
        {
            return true;
        }

        if (config == null || Config == null ||
            config.Count != Config.Count)
        {
            return false;
        }

        foreach (var kvp in config)
        {
            if (!Config.TryGetValue(kvp.Key, out var dependencyConfig) || !kvp.Value.Equals(dependencyConfig))
            {
                return false;
            }
        }

        return true;
    }
}