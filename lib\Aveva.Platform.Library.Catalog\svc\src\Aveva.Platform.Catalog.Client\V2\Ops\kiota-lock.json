{"descriptionHash": "CC81B0B6880ABCBDE4995FEF148614DD9863FCA72FA9C7CB232904D1B4BD46A163E2ABEBE02254C01E4AC14E34A9B6C9C2304B9081582D48833CBEE062803062", "descriptionLocation": "../../../../../../../svc/src/Platform.Catalog/bin/Debug/net8.0/platform-catalog-public-operations-v2-swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.24.3", "clientClassName": "Client", "typeAccessModifier": "Public", "clientNamespaceName": "Aveva.Platform.Catalog.Client.V2.Ops", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": true, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}