<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Asp.Versioning.Http" Version="8.1.0" />
    <PackageVersion Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageVersion Include="Aveva.Platform.AccountMgmt.Client" Version="0.473.5774401" />
    <PackageVersion Include="Aveva.Platform.Authentication.Sdk.KiotaClient" Version="1.3.28" />
    <PackageVersion Include="Aveva.Platform.Authentication.Service.Handler" Version="1.3.28" />
    <PackageVersion Include="Aveva.Platform.Authentication.Sdk.Server" Version="1.2.8" />
    <PackageVersion Include="Aveva.Platform.Authentication.Sdk.S2SCommon" Version="1.3.28" />
    <PackageVersion Include="Aveva.Platform.Authorization.Sdk" Version="0.3.28" />
    <PackageVersion Include="Aveva.Platform.Common.Abstractions" Version="2.0.28" />
    <PackageVersion Include="Aveva.Platform.Common.Framework.AspNetCore" Version="3.0.16" />
    <PackageVersion Include="Aveva.Platform.Common.Framework.ExceptionHandling" Version="3.0.16" />
    <PackageVersion Include="Aveva.Platform.Common.Framework.Abstractions" Version="3.0.16" />
    <PackageVersion Include="Aveva.Platform.Common.Framework.Mapping.AutoMapper" Version="3.0.16" />
    <PackageVersion Include="Aveva.Platform.Common.Monitoring.Instrumentation" Version="3.0.34" />
    <PackageVersion Include="Aveva.Platform.Common.Monitoring.HealthChecks" Version="3.0.34" />
    <PackageVersion Include="Aveva.Platform.Common.Monitoring.HealthChecks.Dapr" Version="3.0.34" />
    <PackageVersion Include="Aveva.Platform.Common.Testing" Version="5.0.152" />
    <PackageVersion Include="Aveva.Platform.Common.Testing.ApiContracts" Version="6.0.7" />
    <PackageVersion Include="Aveva.Platform.Common.Testing.Support" Version="6.0.33" />
    <PackageVersion Include="Aveva.Platform.Common.Messaging.EventBus" Version="3.0.71" />
    <PackageVersion Include="Aveva.Platform.Common.Messaging.EventBus.Events" Version="3.0.71" />
    <PackageVersion Include="Aveva.Platform.InstanceMgmt.Client" Version="0.479.5844831" />
    <PackageVersion Include="Aveva.Ruleset" Version="1.0.8.4" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="coverlet.msbuild" Version="6.0.4" />
    <PackageVersion Include="Dapr.AspNetCore" Version="1.15.4" />
    <PackageVersion Include="Dapr.Client" Version="1.15.4" />
    <PackageVersion Include="Dapr.Extensions.Configuration" Version="1.15.4" />
    <PackageVersion Include="KubernetesClient" Version="16.0.7" />
    <PackageVersion Include="KubeOps.Generator" Version="9.6.0" />
    <PackageVersion Include="KubeOps.Operator.Web" Version="9.6.0" />
    <PackageVersion Include="Localtunnel" Version="2.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.15" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.15" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="9.0.5" />
    <PackageVersion Include="Microsoft.OpenApi.Readers" Version="1.6.24" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
    <PackageVersion Include="Polly" Version="8.5.2" />
    <PackageVersion Include="RabbitMQ.Client" Version="6.8.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Swagger" Version="8.1.2" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Microsoft.SourceLink.AzureRepos.Git" Version="8.0.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="VogueDeputy" Version="3.2.0" />
    <PackageVersion Include="xunit.v3" Version="2.0.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.0" />
    <PackageVersion Include="Microsoft.Kiota.Abstractions" Version="1.17.1" />
    <PackageVersion Include="Microsoft.Kiota.Http.HttpClientLibrary" Version="1.17.1" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Form" Version="1.17.1" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Json" Version="1.17.1" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Multipart" Version="1.17.1" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Text" Version="1.17.1" />
    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageVersion Include="System.Text.Json" Version="8.0.5" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>