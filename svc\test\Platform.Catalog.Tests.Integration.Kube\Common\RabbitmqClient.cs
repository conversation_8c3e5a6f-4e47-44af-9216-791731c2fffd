﻿using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.Json;
using Aveva.Platform.Catalog.Domain.Serialization;
using Aveva.Platform.Catalog.Tests.Integration.Kube.Common;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace Platform.Catalog.Tests.Integration.Kube.Common
{
    [SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
    [SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
    internal sealed class RabbitmqClient
    {
        private readonly ConnectionFactory _connectionFactory;
        private readonly IConnection _connection;
        private readonly IModel _session;
        private readonly int _timeout;
        private readonly ILogger _logger;

        public RabbitmqClient(string host, int port, string adminUserName, string adminSecret, ILogger logger, int consumerWaitTimeInMins = 10)
        {
            ArgumentException.ThrowIfNullOrEmpty(host, nameof(host));
            ArgumentException.ThrowIfNullOrEmpty(adminUserName, nameof(adminUserName));
            ArgumentException.ThrowIfNullOrEmpty(adminSecret, nameof(adminSecret));

            _logger = logger;
            _timeout = consumerWaitTimeInMins;
            _connectionFactory = new ConnectionFactory
            {
                HostName = host,
                UserName = adminUserName,
                Password = adminSecret,
                Port = port,
            };

            _connection = _connectionFactory.CreateConnection();
            _session = _connection.CreateModel();
        }

        public ConcurrentBag<DaprCloudEvent> EventsReceived { get; private set; } = [];

        public void RegisterEventListenerAsync(List<string> catalogEvents)
        {
            var queueName = _session.QueueDeclare().QueueName;
            _logger?.LogInformation($"{queueName} Queue created in Rabbitmq.");
            foreach (var catalogEvent in catalogEvents)
            {
                _session.ExchangeDeclare(exchange: catalogEvent, type: ExchangeType.Fanout, durable: true, autoDelete: true);
                _session.QueueBind(queue: queueName, exchange: catalogEvent, routingKey: string.Empty, null);
            }

            var eventConsumer = new EventingBasicConsumer(_session);
            eventConsumer.Received += EventReceivedHandler;

            _session.BasicConsume(queue: queueName, autoAck: true, consumer: eventConsumer);

            Task.Delay(TimeSpan.FromMinutes(_timeout)).Wait();
        }

        public int GetEventCount<T>(string serviceEntryId)
        {
            ArgumentException.ThrowIfNullOrEmpty(serviceEntryId, nameof(serviceEntryId));
            var eventType = typeof(T).Name;
            return EventsReceived.Where(x => x.Topic.Equals(eventType, StringComparison.OrdinalIgnoreCase) && x.Data.Id.Equals(serviceEntryId, StringComparison.OrdinalIgnoreCase)).Count();
        }

        public void EventReceivedHandler(object? sender, BasicDeliverEventArgs message)
        {
            var messageBody = Encoding.UTF8.GetString(message.Body.ToArray());
            _logger?.LogInformation($"Consumer received Catalog Event = {messageBody}");
            var deserializedEvent = JsonSerializer.Deserialize<DaprCloudEvent>(messageBody, JsonSerializationOptions.Options);
            if (deserializedEvent != null)
            {
                EventsReceived.Add(deserializedEvent);
                _logger?.LogInformation($"Added Event = {deserializedEvent.Topic} to concurrentbag.");
            }
        }
    }
}