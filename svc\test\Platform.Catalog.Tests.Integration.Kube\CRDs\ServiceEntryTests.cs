﻿using System.Net.Http.Json;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Serialization;
using Aveva.Platform.Catalog.Infrastructure.Entities;
using Aveva.Platform.Catalog.Tests.Integration.Kube;
using Aveva.Platform.Catalog.Tests.Integration.Kube.Common;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using k8s.Models;
using Shouldly;
using ApiModels = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;
using V1Lifecycle = Aveva.Platform.Catalog.Domain.Models.V1Lifecycle;

namespace Platform.Catalog.Tests.Integration.Kube.CRDs;

[Collection(IntegrationTestCollectionFixture.Name)]
public class ServiceEntryTests(IntegrationTestFixture fixture)
{
    private readonly IntegrationTestFixture _fixture = fixture;
    private readonly string _apiV2Route = "/api/account/abc/v2/services";
    private readonly string _opsV2Route = "/ops/v2/services";
    private readonly List<string> _testServiceEntryIds = ["account-shared", "catalog-isolated", "lifecycle-none", "service-with-apps"];

    [Fact]
    public async Task ServiceEntryTests_Catalog_ApiV2_Routes()
    {
        async Task<Tuple<ApiModels.ServiceCollectionResponse?, bool>> ApiV2ResponseCheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ApiModels.ServiceCollectionResponse>(_apiV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            return new Tuple<ApiModels.ServiceCollectionResponse?, bool>(response, response?.Items?.Count >= 4);
        }

        var responseV2 = await WaitForCondition.Until(ApiV2ResponseCheck).ConfigureAwait(true);

        responseV2.ShouldNotBeNull();
        responseV2!.Items!.Count.ShouldBeGreaterThanOrEqualTo(4);
        _testServiceEntryIds.ShouldBeSubsetOf(responseV2.Items!.Select(x => x.Id!));
    }

    [Fact]
    public async Task ServiceEntryTests_Catalog_OpsV2_Routes()
    {
        async Task<Tuple<ServiceCollectionResponse, bool>> OpsV2ResponseCheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            return new Tuple<ServiceCollectionResponse, bool>(response!, response?.Items?.Count >= 4);
        }

        var responseV2 = await WaitForCondition.Until(OpsV2ResponseCheck).ConfigureAwait(true);

        responseV2.ShouldNotBeNull();
        responseV2!.Items!.Count.ShouldBeGreaterThanOrEqualTo(4);
        _testServiceEntryIds.ShouldBeSubsetOf(responseV2.Items!.Select(x => x.Id!));
    }

    [Fact]
    public async Task ServiceEntryTests_Create()
    {
        // Add a new dependency service entry
        var dependencyTestServiceEntryId = "dep1";
        await CreateNewServiceEntry(dependencyTestServiceEntryId, dependencyTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        var createTestServiceEntryId = "shared-create";

        // Add a new service entry
        await CreateNewServiceEntry(createTestServiceEntryId, createTestServiceEntryId, _fixture.Options.TestDeploymentNamespace, dependencies: CreateDependencyConfig(dependencyTestServiceEntryId));

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(createTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(createTestServiceEntryId);

        Task<bool> AddEventsCondition()
        {
            var addEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceAddV1>(createTestServiceEntryId);
            return Task.FromResult(addEvents == 1);
        }

        await WaitForCondition.Until(AddEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_Update()
    {
        // Add a new dependency service entry
        var dependencyTestServiceEntryId = "dep2";
        await CreateNewServiceEntry(dependencyTestServiceEntryId, dependencyTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        var updateTestServiceEntryId = "shared-update";

        // Add a new service entry
        await CreateNewServiceEntry(updateTestServiceEntryId, updateTestServiceEntryId, _fixture.Options.TestDeploymentNamespace, dependencies: CreateDependencyConfig(dependencyTestServiceEntryId));

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(updateTestServiceEntryId);

        // Update an existing CR
        var patch = "[ { \"op\": \"replace\", \"path\": \"/spec/displayName\", \"value\": \"test\" }]";
        V1Patch kubePatch = new V1Patch(patch, V1Patch.PatchType.JsonPatch);
        await _fixture.GenericClient.PatchNamespacedAsync<V1K8sServiceEntry>(kubePatch, _fixture.Options.TestDeploymentNamespace, updateTestServiceEntryId, cancel: TestContext.Current.CancellationToken).ConfigureAwait(true);

        async Task<Tuple<ServiceResponse, bool>> UpdateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var updatedServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(updatedServiceEntry!, updatedServiceEntry!.DisplayName!.Equals("test", StringComparison.InvariantCultureIgnoreCase));
        }

        var updatedServiceEntry = await WaitForCondition.Until(UpdateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        updatedServiceEntry.DisplayName.ShouldBe("test");

        Task<bool> UpdateEventsCondition()
        {
            var updateEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceUpdateV1>(updateTestServiceEntryId);
            return Task.FromResult(updateEvents >= 1);
        }

        await WaitForCondition.Until(UpdateEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_Delete()
    {
        var deleteTestServiceEntryId = "shared-delete";

        // Add a new service entry
        await CreateNewServiceEntry(deleteTestServiceEntryId, deleteTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(deleteTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(deleteTestServiceEntryId);

        // Delete an existing CR
        await _fixture.GenericClient.DeleteNamespacedAsync<V1Status>(_fixture.Options.TestDeploymentNamespace, deleteTestServiceEntryId, CancellationToken.None).ConfigureAwait(true);

        async Task<bool> DeleteServiceEntryCheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var deletedServiceEntry = response?.Items!.Where(x => x.Id!.Equals(deleteTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return deletedServiceEntry == null;
        }

        await WaitForCondition.Until(DeleteServiceEntryCheck, 10000).ConfigureAwait(true);

        Task<bool> DeleteEventCondition()
        {
            var deleteEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceDeleteV1>(deleteTestServiceEntryId);
            return Task.FromResult(deleteEvents == 1);
        }

        await WaitForCondition.Until(DeleteEventCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenLegacyProtocol_Create()
    {
        // Add a new dependency service entry
        var dependencyTestServiceEntryId = "dep3";
        await CreateNewServiceEntry(dependencyTestServiceEntryId, dependencyTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        var createTestServiceEntryId = "legacy-create";

        // Add a new service entry
        await CreateNewServiceEntry(
            createTestServiceEntryId,
            createTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            dependencies: CreateDependencyConfig(dependencyTestServiceEntryId),
            lifecycle: new V1Lifecycle
            {
                Protocol = V1IntegrationProtocol.Legacy,
                ProtocolOptions = new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "cap1",
                            },
                        ],
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "eu-west-1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                dependencyTestServiceEntryId, new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "int1",
                                    SourceContextConfig = "sourceContext",
                                    TargetContextConfig = null,
                                }
                            },
                        },
                    },
                },
            });

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(createTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(createTestServiceEntryId);

        Task<bool> AddEventsCondition()
        {
            var addEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceAddV1>(createTestServiceEntryId);
            return Task.FromResult(addEvents == 1);
        }

        await WaitForCondition.Until(AddEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenLegacyProtocol_Update()
    {
        // Add a new dependency service entry
        var dependencyTestServiceEntryId = "dep4";
        await CreateNewServiceEntry(dependencyTestServiceEntryId, dependencyTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        var updateTestServiceEntryId = "legacy-update";

        // Add a new service entry
        await CreateNewServiceEntry(
            updateTestServiceEntryId,
            updateTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            dependencies: CreateDependencyConfig(dependencyTestServiceEntryId),
            lifecycle: new V1Lifecycle
            {
                Protocol = V1IntegrationProtocol.Legacy,
                ProtocolOptions = new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "cap1",
                            },
                        ],
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "eu-west-1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                dependencyTestServiceEntryId, new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "int1",
                                    SourceContextConfig = "sourceContext",
                                    TargetContextConfig = null,
                                }
                            },
                        },
                    },
                },
            });

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(updateTestServiceEntryId);

        // Update an existing CR
        var patch = "[ { \"op\": \"replace\", \"path\": \"/spec/displayName\", \"value\": \"test\" }]";
        V1Patch kubePatch = new V1Patch(patch, V1Patch.PatchType.JsonPatch);
        await _fixture.GenericClient.PatchNamespacedAsync<V1K8sServiceEntry>(kubePatch, _fixture.Options.TestDeploymentNamespace, updateTestServiceEntryId, cancel: TestContext.Current.CancellationToken).ConfigureAwait(true);

        async Task<Tuple<ServiceResponse, bool>> UpdateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var updatedServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(updatedServiceEntry!, updatedServiceEntry!.DisplayName!.Equals("test", StringComparison.InvariantCultureIgnoreCase));
        }

        var updatedServiceEntry = await WaitForCondition.Until(UpdateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        updatedServiceEntry.DisplayName.ShouldBe("test");

        Task<bool> UpdateEventsCondition()
        {
            var updateEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceUpdateV1>(updateTestServiceEntryId);
            return Task.FromResult(updateEvents >= 1);
        }

        await WaitForCondition.Until(UpdateEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenWebhookProtocol_Create()
    {
        // Add a new dependency service entry
        var dependencyTestServiceEntryId = "dep5";
        await CreateNewServiceEntry(dependencyTestServiceEntryId, dependencyTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        var createTestServiceEntryId = "webhook-create";

        // Add a new service entry
        await CreateNewServiceEntry(
            createTestServiceEntryId,
            createTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            dependencies: CreateDependencyConfig(dependencyTestServiceEntryId),
            lifecycle: new V1Lifecycle
            {
                Protocol = V1IntegrationProtocol.Webhook,
                ProtocolOptions = new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.example.com/webhook"),
                },
                Trigger = V1Trigger.Catalog,
                InstanceMode = V1InstanceMode.Isolated,
                ProviderId = "provider",
            });

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(createTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(createTestServiceEntryId);

        Task<bool> AddEventsCondition()
        {
            var addEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceAddV1>(createTestServiceEntryId);
            return Task.FromResult(addEvents == 1);
        }

        await WaitForCondition.Until(AddEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenWebhookProtocol_Update()
    {
        // Add a new dependency service entry
        var dependencyTestServiceEntryId = "dep6";
        await CreateNewServiceEntry(dependencyTestServiceEntryId, dependencyTestServiceEntryId, _fixture.Options.TestDeploymentNamespace);

        var updateTestServiceEntryId = "webhook-update";

        // Add a new service entry
        await CreateNewServiceEntry(
            updateTestServiceEntryId,
            updateTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            dependencies: CreateDependencyConfig(dependencyTestServiceEntryId),
            lifecycle: new V1Lifecycle
            {
                Protocol = V1IntegrationProtocol.Webhook,
                ProtocolOptions = new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.example.com/webhook"),
                },
                Trigger = V1Trigger.Catalog,
                InstanceMode = V1InstanceMode.Isolated,
                ProviderId = "provider",
            });

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(updateTestServiceEntryId);

        // Update an existing CR
        var patch = "[ { \"op\": \"replace\", \"path\": \"/spec/displayName\", \"value\": \"test\" }]";
        V1Patch kubePatch = new V1Patch(patch, V1Patch.PatchType.JsonPatch);
        await _fixture.GenericClient.PatchNamespacedAsync<V1K8sServiceEntry>(kubePatch, _fixture.Options.TestDeploymentNamespace, updateTestServiceEntryId, cancel: TestContext.Current.CancellationToken).ConfigureAwait(true);

        async Task<Tuple<ServiceResponse, bool>> UpdateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var updatedServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(updatedServiceEntry!, updatedServiceEntry!.DisplayName!.Equals("test", StringComparison.InvariantCultureIgnoreCase));
        }

        var updatedServiceEntry = await WaitForCondition.Until(UpdateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        updatedServiceEntry.DisplayName.ShouldBe("test");

        Task<bool> UpdateEventsCondition()
        {
            var updateEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceUpdateV1>(updateTestServiceEntryId);
            return Task.FromResult(updateEvents >= 1);
        }

        await WaitForCondition.Until(UpdateEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenExternal_Create()
    {
        var createTestServiceEntryId = "external-create";

        var geographies = new List<V1Geography> { new V1Geography { Id = "eu" } };

        // Add a new service entry
        await CreateNewServiceEntry(
            createTestServiceEntryId,
            createTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            hostingType: V1HostingType.External,
            geographies: geographies);

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(createTestServiceEntryId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(createTestServiceEntryId);

        Task<bool> AddEventsCondition()
        {
            var addEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceAddV1>(createTestServiceEntryId);
            return Task.FromResult(addEvents == 1);
        }

        await WaitForCondition.Until(AddEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenExternal_Update()
    {
        var updateTestServiceEntryId = "external-update";

        var geographies = new List<V1Geography> { new V1Geography { Id = "eu" } };

        // Add a new service entry
        await CreateNewServiceEntry(
            updateTestServiceEntryId,
            updateTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            hostingType: V1HostingType.External,
            geographies: geographies);

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(updateTestServiceEntryId);

        // Update an existing CR
        var patch = "[ { \"op\": \"add\", \"path\": \"/spec/geographies/-\", \"value\": { \"id\": \"us\" } }]";
        V1Patch kubePatch = new V1Patch(patch, V1Patch.PatchType.JsonPatch);
        await _fixture.GenericClient.PatchNamespacedAsync<V1K8sServiceEntry>(kubePatch, _fixture.Options.TestDeploymentNamespace, updateTestServiceEntryId, cancel: TestContext.Current.CancellationToken).ConfigureAwait(true);

        async Task<Tuple<ServiceResponse, bool>> UpdateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var updatedServiceEntry = response?.Items!.Where(x => x.Id!.Equals(updateTestServiceEntryId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(updatedServiceEntry!, updatedServiceEntry!.Geographies!.Exists(x => x.Id!.Equals("us", StringComparison.OrdinalIgnoreCase)));
        }

        var updatedServiceEntry = await WaitForCondition.Until(UpdateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        updatedServiceEntry.Geographies!.ShouldNotBeEmpty();
        updatedServiceEntry.Geographies!.ShouldContain(x => x.Id!.Equals("us", StringComparison.OrdinalIgnoreCase));

        Task<bool> UpdateEventsCondition()
        {
            var updateEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceUpdateV1>(updateTestServiceEntryId);
            return Task.FromResult(updateEvents >= 1);
        }

        await WaitForCondition.Until(UpdateEventsCondition, 10000).ConfigureAwait(true);
    }

    [Fact]
    public async Task ServiceEntryTests_WhenExternal_Remove_Geo()
    {
        var removeGeoTestServiceEntryId = "external-remove-geo";

        var geographies = new List<V1Geography> { new V1Geography { Id = "eu" }, new V1Geography { Id = "us" } };

        // Add a new service entry
        await CreateNewServiceEntry(
            removeGeoTestServiceEntryId,
            removeGeoTestServiceEntryId,
            _fixture.Options.TestDeploymentNamespace,
            hostingType: V1HostingType.External,
            geographies: geographies);

        async Task<Tuple<ServiceResponse, bool>> CreateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var createdServiceEntry = response?.Items!.Where(x => x.Id!.Equals(removeGeoTestServiceEntryId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(createdServiceEntry!, createdServiceEntry != null);
        }

        var createdServiceEntry = await WaitForCondition.Until(CreateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        createdServiceEntry.ShouldNotBeNull();
        createdServiceEntry.Id.ShouldBe(removeGeoTestServiceEntryId);

        // Update an existing CR
        var patch = "[ { \"op\": \"remove\", \"path\": \"/spec/geographies/1\" }]";
        V1Patch kubePatch = new V1Patch(patch, V1Patch.PatchType.JsonPatch);
        await _fixture.GenericClient.PatchNamespacedAsync<V1K8sServiceEntry>(kubePatch, _fixture.Options.TestDeploymentNamespace, removeGeoTestServiceEntryId, cancel: TestContext.Current.CancellationToken).ConfigureAwait(true);

        async Task<Tuple<ServiceResponse, bool>> UpdateServiceEntryOpsRoutecheck()
        {
            var response = await _fixture.TestClient.GetFromJsonAsync<ServiceCollectionResponse>(_opsV2Route, JsonSerializationOptions.Options).ConfigureAwait(true);
            var updatedServiceEntry = response?.Items!.Where(x => x.Id!.Equals(removeGeoTestServiceEntryId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
            return new Tuple<ServiceResponse, bool>(updatedServiceEntry!, !updatedServiceEntry!.Geographies!.Exists(x => x.Id!.Equals("us", StringComparison.OrdinalIgnoreCase)));
        }

        var updatedServiceEntry = await WaitForCondition.Until(UpdateServiceEntryOpsRoutecheck).ConfigureAwait(true);
        updatedServiceEntry.Geographies!.ShouldNotBeEmpty();
        updatedServiceEntry.Geographies!.ShouldNotContain(x => x.Id!.Equals("us", StringComparison.OrdinalIgnoreCase));

        Task<bool> UpdateEventsCondition()
        {
            var updateEvents = _fixture.RabbitmqClient!.GetEventCount<CatalogServiceUpdateV1>(removeGeoTestServiceEntryId);
            return Task.FromResult(updateEvents >= 1);
        }

        await WaitForCondition.Until(UpdateEventsCondition, 10000).ConfigureAwait(true);
    }

    private static Dictionary<string, V1CatalogDataDependency> CreateDependencyConfig(string dependencyTestServiceEntryId)
        => new()
        {
            {
                dependencyTestServiceEntryId,
                new V1CatalogDataDependency
                {
                    Type = V1CatalogDataDependencyType.Optional,
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        {
                            "sourceContext",
                            new V1CatalogDataDependencyConfig
                            {
                                Required = true,
                                Max = 255,
                                Label = "Source Context",
                                Help = "The source context configuration for the dependency",
                            }
                        },
                    },
                }
            },
        };

    private async Task CreateNewServiceEntry(
        string serviceEntryId,
        string serviceEntryName,
        string k8sNamespace,
        V1Category category = V1Category.Data,
        V1HostingType hostingType = V1HostingType.Environment,
        V1Lifecycle? lifecycle = null,
        List<V1Geography>? geographies = null,
        Dictionary<string, V1CatalogDataDependency>? dependencies = null)
    {
        ArgumentException.ThrowIfNullOrEmpty(serviceEntryName, nameof(serviceEntryName));
        ArgumentException.ThrowIfNullOrEmpty(k8sNamespace, nameof(k8sNamespace));

        // Add a new service entry
        var serviceEntry = new V1ServiceEntry()
        {
            Id = serviceEntryId,
            DisplayName = serviceEntryName,
            Category = category,
            HostingType = hostingType,
            Lifecycle = lifecycle ?? new V1Lifecycle()
            {
                Trigger = V1Trigger.None,
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
            },
            Geographies = geographies,
            Dependencies = dependencies,
        };

        V1K8sServiceEntry k8sServiceEntry = V1K8sServiceEntry.Create(serviceEntry, k8sNamespace);
        k8sServiceEntry.ApiVersion = "servicecatalog.aveva.com/v1";
        k8sServiceEntry.Kind = "ServiceEntry";
        k8sServiceEntry.Metadata = new V1ObjectMeta()
        {
            Name = serviceEntryName,
            NamespaceProperty = k8sNamespace,
        };

        await _fixture.GenericClient.CreateNamespacedAsync(k8sServiceEntry, k8sNamespace).ConfigureAwait(true);
    }
}