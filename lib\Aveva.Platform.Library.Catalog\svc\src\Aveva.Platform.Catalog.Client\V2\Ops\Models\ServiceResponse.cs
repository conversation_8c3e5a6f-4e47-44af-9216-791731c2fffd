// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>
    /// Represents a catalog service entry with detailed information about the service and its capabilities.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ServiceResponse : IParsable
    {
        /// <summary>Applications associated with this service. Each application represents a discrete component or interface that provides specific functionality within the service.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Application>? Applications { get; set; }
#nullable restore
#else
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Application> Applications { get; set; }
#endif
        /// <summary>Defines provisioning availability and constraints for a service. This resource contains information about how a service can be discovered and provisioned to an account.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability? Availability { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability Availability { get; set; }
#endif
        /// <summary>The category property</summary>
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.CategoryNullable? Category { get; set; }
        /// <summary>Other services that this service depends on to function properly. Keys represent the dependency identifier, and values contain details about the dependency relationship including cardinality and configuration requirements.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse_dependencies? Dependencies { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse_dependencies Dependencies { get; set; }
#endif
        /// <summary>A detailed description of the service explaining its purpose, capabilities, and key features. This text helps users understand what the service does and when to use it.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The user-friendly name of the service displayed in user interfaces. This is the primary label used to identify the service to end users.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? DisplayName { get; set; }
#nullable restore
#else
        public string DisplayName { get; set; }
#endif
        /// <summary>External identity providers associated with this service. These identities enable integration with third-party authentication and authorization systems.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity>? ExternalIdentities { get; set; }
#nullable restore
#else
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity> ExternalIdentities { get; set; }
#endif
        /// <summary>Geographic regions where this service can be provisioned. Only applicable and required for services with `External` hosting type. Each geography represents a distinct region where the service can be deployed and accessed. For non-external hosting types, this collection should be empty.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Geography>? Geographies { get; set; }
#nullable restore
#else
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Geography> Geographies { get; set; }
#endif
        /// <summary>The deployment scope of the service. Valid values include:            `Environment` (available in all regions and geographies),            `Geography` (available in all regions within a specific geography),            `Regional` (available in a specific region within a specific geography),            `External` (hosted outside the platform but accessible through platform integration).</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? HostingType { get; set; }
#nullable restore
#else
        public string HostingType { get; set; }
#endif
        /// <summary>A URL pointing to the service&apos;s icon image stored in the AVEVA CDN. When using this URL, implement proper cache control to ensure users see updated icons when they change. Browsers should be configured to periodically check for updates rather than caching indefinitely.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? IconUrl { get; set; }
#nullable restore
#else
        public string IconUrl { get; set; }
#endif
        /// <summary>The unique identifier for this service. This ID is used to reference the service in other operations.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Id { get; set; }
#nullable restore
#else
        public string Id { get; set; }
#endif
        /// <summary>Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Lifecycle? Lifecycle { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Lifecycle Lifecycle { get; set; }
#endif
        /// <summary>Terms used to categorize and filter the service. Tags provide additional classification beyond the primary category and help users discover related services.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Tags { get; set; }
#nullable restore
#else
        public List<string> Tags { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "applications", n => { Applications = n.GetCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Application>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Application.CreateFromDiscriminatorValue)?.AsList(); } },
                { "availability", n => { Availability = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability.CreateFromDiscriminatorValue); } },
                { "category", n => { Category = n.GetEnumValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.CategoryNullable>(); } },
                { "dependencies", n => { Dependencies = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse_dependencies>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse_dependencies.CreateFromDiscriminatorValue); } },
                { "description", n => { Description = n.GetStringValue(); } },
                { "displayName", n => { DisplayName = n.GetStringValue(); } },
                { "externalIdentities", n => { ExternalIdentities = n.GetCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity.CreateFromDiscriminatorValue)?.AsList(); } },
                { "geographies", n => { Geographies = n.GetCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Geography>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Geography.CreateFromDiscriminatorValue)?.AsList(); } },
                { "hostingType", n => { HostingType = n.GetStringValue(); } },
                { "iconUrl", n => { IconUrl = n.GetStringValue(); } },
                { "id", n => { Id = n.GetStringValue(); } },
                { "lifecycle", n => { Lifecycle = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Lifecycle>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Lifecycle.CreateFromDiscriminatorValue); } },
                { "tags", n => { Tags = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Application>("applications", Applications);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability>("availability", Availability);
            writer.WriteEnumValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.CategoryNullable>("category", Category);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceResponse_dependencies>("dependencies", Dependencies);
            writer.WriteStringValue("description", Description);
            writer.WriteStringValue("displayName", DisplayName);
            writer.WriteCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity>("externalIdentities", ExternalIdentities);
            writer.WriteCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Geography>("geographies", Geographies);
            writer.WriteStringValue("hostingType", HostingType);
            writer.WriteStringValue("iconUrl", IconUrl);
            writer.WriteStringValue("id", Id);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.Lifecycle>("lifecycle", Lifecycle);
            writer.WriteCollectionOfPrimitiveValues<string>("tags", Tags);
        }
    }
}
#pragma warning restore CS0618
