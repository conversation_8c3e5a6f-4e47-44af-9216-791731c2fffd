{"descriptionHash": "7F84AB3DAD8EF492333CD49F2FDC5506804BA09B55179C18E133B04F0AEADA627B1D30F035673B04C67F0CB2FF6444F52B5C2E0A2F253CC56419AAADEE507787", "descriptionLocation": "../../../../../../../svc/src/Platform.Catalog/bin/Debug/net8.0/platform-catalog-public-operations-v2-swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.24.3", "clientClassName": "Client", "typeAccessModifier": "Public", "clientNamespaceName": "Aveva.Platform.Catalog.Client.V2.Ops", "language": "TypeScript", "usesBackingStore": false, "excludeBackwardCompatible": true, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}