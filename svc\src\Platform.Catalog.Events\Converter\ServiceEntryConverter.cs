﻿using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;

namespace Aveva.Platform.Catalog.Events.Converter
{
    /// <summary>
    /// Converts catalog service domain models to their corresponding DTO representations.
    /// Handles the conversion of service entries, lifecycle data, and dependencies.
    /// </summary>
    public static class ServiceEntryConverter
    {
        /// <summary>
        /// Converts a domain V1ServiceEntry to a DTO ServiceEntryV1.
        /// </summary>
        public static ServiceEntryV1 ToDto(this V1ServiceEntry domainModel)
        {
            ArgumentNullException.ThrowIfNull(domainModel);

            return new ServiceEntryV1
            {
                Id = domainModel.Id,
                DisplayName = domainModel.DisplayName,
                IconUrl = domainModel.IconUrl,
                Description = domainModel.Description,
                HostingType = domainModel.HostingType.ToString(),
                Tags = domainModel.Tags?.ToList(),
                Lifecycle = domainModel.Lifecycle?.ToDto(),
                Applications = domainModel.Applications != null
                    ? domainModel.Applications.Select(x => x.ToDto()).ToList()
                    : new List<ApplicationV1>(),
                Dependencies = domainModel.Dependencies != null
                    ? domainModel.Dependencies.ToDictionary(x => x.Key, x => x.Value.ToDto())
                    : new Dictionary<string, CatalogDataDependencyV1>(),
            };
        }

        /// <summary>
        /// Converts a domain V1Lifecycle to a DTO LifecycleV1.
        /// </summary>
        public static LifecycleV1 ToDto(this V1Lifecycle domainModel)
        {
            ArgumentNullException.ThrowIfNull(domainModel);

            return new LifecycleV1
            {
                Trigger = domainModel.Trigger.ToString(),
                Protocol = domainModel.Protocol?.ToString(),
                ProviderId = domainModel.ProviderId,
                InstanceMode = domainModel.InstanceMode?.ToString(),
            };
        }

        /// <summary>
        /// Converts a domain V1CatalogDataDependency to a DTO CatalogDataDependencyV1.
        /// </summary>
        public static CatalogDataDependencyV1 ToDto(this V1CatalogDataDependency domainModel)
        {
            ArgumentNullException.ThrowIfNull(domainModel);

            return new CatalogDataDependencyV1
            {
                Type = (CatalogDataDependencyTypeV1)domainModel.Type,
                Cardinality = (CatalogDataDependencyCardinalityV1)domainModel.Cardinality,
                Colocated = domainModel.Colocated ?? false,
                Config = domainModel.Config != null
                    ? domainModel.Config.ToDictionary(x => x.Key, x => x.Value.ToDto())
                    : new Dictionary<string, CatalogDataDependencyConfigV1>(),
            };
        }

        /// <summary>
        /// Converts a domain V1CatalogDataDependencyConfig to a DTO CatalogDataDependencyConfigV1.
        /// </summary>
        public static CatalogDataDependencyConfigV1 ToDto(this V1CatalogDataDependencyConfig domainModel)
        {
            ArgumentNullException.ThrowIfNull(domainModel);

            return new CatalogDataDependencyConfigV1
            {
                Label = domainModel.Label,
                Help = domainModel.Help,
                Required = domainModel.Required,
                Min = domainModel.Min,
                Max = domainModel.Max,
            };
        }

        /// <summary>
        /// Converts a domain application to its DTO representation.
        /// </summary>
        public static ApplicationV1 ToDto(this V1Application domainModel)
        {
            ArgumentNullException.ThrowIfNull(domainModel);

            return new ApplicationV1
            {
                Name = domainModel.Name,
                Urls = domainModel.Urls != null
                    ? domainModel.Urls.ToDictionary(x => x.Key, x => x.Value)
                    : new Dictionary<string, string>(),
            };
        }
    }
}