/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { type AdditionalDataHolder, type Parsable, type ParseNode, type SerializationWriter } from '@microsoft/kiota-abstractions';

/**
 * Defines service applications, where each additional property represents an application. A representation of an application that belongs to a service in the catalog, containing information like name and access URLs.
 */
export interface Application extends Parsable {
    /**
     * The name of the application. The unique name that identifies this application within a service. This name is used for display and reference purposes.
     */
    name?: string | null;
    /**
     * Optional URLs keyed to launch for specific valid geographies and/or default URLs to launch if a geography is not identified. A dictionary of URLs where keys represent geography codes and values are the corresponding application launch URLs. If any URLs are provided, a `default` key must be included to specify the URL to use when no specific geography match is found.
     */
    urls?: Application_urls | null;
}
/**
 * Optional URLs keyed to launch for specific valid geographies and/or default URLs to launch if a geography is not identified. A dictionary of URLs where keys represent geography codes and values are the corresponding application launch URLs. If any URLs are provided, a `default` key must be included to specify the URL to use when no specific geography match is found.
 */
export interface Application_urls extends AdditionalDataHolder, Parsable {
    /**
     * Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.
     */
    additionalData?: Record<string, unknown>;
}
export type CategoryNullable = (typeof CategoryNullableObject)[keyof typeof CategoryNullableObject];
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {Application_urls}
 */
// @ts-ignore
export function createApplication_urlsFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoApplication_urls;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {Application}
 */
// @ts-ignore
export function createApplicationFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoApplication;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {ExternalIdentity}
 */
// @ts-ignore
export function createExternalIdentityFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoExternalIdentity;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {Geography}
 */
// @ts-ignore
export function createGeographyFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoGeography;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {LegacyProtocolApplicationMapping}
 */
// @ts-ignore
export function createLegacyProtocolApplicationMappingFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoLegacyProtocolApplicationMapping;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {LegacyProtocolMappings_dependencies}
 */
// @ts-ignore
export function createLegacyProtocolMappings_dependenciesFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoLegacyProtocolMappings_dependencies;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {LegacyProtocolMappings_geographies}
 */
// @ts-ignore
export function createLegacyProtocolMappings_geographiesFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoLegacyProtocolMappings_geographies;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {LegacyProtocolMappings}
 */
// @ts-ignore
export function createLegacyProtocolMappingsFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoLegacyProtocolMappings;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {LegacyProtocolOptions}
 */
// @ts-ignore
export function createLegacyProtocolOptionsFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoLegacyProtocolOptions;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {Lifecycle}
 */
// @ts-ignore
export function createLifecycleFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoLifecycle;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {LegacyProtocolOptions | WebhookProtocolOptions}
 */
// @ts-ignore
export function createProtocolOptionsFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoProtocolOptions;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {ServiceAvailability}
 */
// @ts-ignore
export function createServiceAvailabilityFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoServiceAvailability;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {ServiceCollectionResponse}
 */
// @ts-ignore
export function createServiceCollectionResponseFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoServiceCollectionResponse;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {ServiceResponse_dependencies}
 */
// @ts-ignore
export function createServiceResponse_dependenciesFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoServiceResponse_dependencies;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {ServiceResponse}
 */
// @ts-ignore
export function createServiceResponseFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoServiceResponse;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {WebhookProtocolOptions}
 */
// @ts-ignore
export function createWebhookProtocolOptionsFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoWebhookProtocolOptions;
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoApplication(application: Partial<Application> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "name": n => { application.name = n.getStringValue(); },
        "urls": n => { application.urls = n.getObjectValue<Application_urls>(createApplication_urlsFromDiscriminatorValue); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoApplication_urls(application_urls: Partial<Application_urls> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoExternalIdentity(externalIdentity: Partial<ExternalIdentity> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "id": n => { externalIdentity.id = n.getStringValue(); },
        "scopes": n => { externalIdentity.scopes = n.getCollectionOfEnumValues<ExternalIdentityScope>(ExternalIdentityScopeObject); },
        "type": n => { externalIdentity.type = n.getEnumValue<ExternalIdentityType>(ExternalIdentityTypeObject); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoGeography(geography: Partial<Geography> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "id": n => { geography.id = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoLegacyProtocolApplicationMapping(legacyProtocolApplicationMapping: Partial<LegacyProtocolApplicationMapping> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "capabilityDefinition": n => { legacyProtocolApplicationMapping.capabilityDefinition = n.getStringValue(); },
        "name": n => { legacyProtocolApplicationMapping.name = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoLegacyProtocolMappings(legacyProtocolMappings: Partial<LegacyProtocolMappings> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "applications": n => { legacyProtocolMappings.applications = n.getCollectionOfObjectValues<LegacyProtocolApplicationMapping>(createLegacyProtocolApplicationMappingFromDiscriminatorValue); },
        "dependencies": n => { legacyProtocolMappings.dependencies = n.getObjectValue<LegacyProtocolMappings_dependencies>(createLegacyProtocolMappings_dependenciesFromDiscriminatorValue); },
        "geographies": n => { legacyProtocolMappings.geographies = n.getObjectValue<LegacyProtocolMappings_geographies>(createLegacyProtocolMappings_geographiesFromDiscriminatorValue); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoLegacyProtocolMappings_dependencies(legacyProtocolMappings_dependencies: Partial<LegacyProtocolMappings_dependencies> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoLegacyProtocolMappings_geographies(legacyProtocolMappings_geographies: Partial<LegacyProtocolMappings_geographies> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoLegacyProtocolOptions(legacyProtocolOptions: Partial<LegacyProtocolOptions> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "mappings": n => { legacyProtocolOptions.mappings = n.getObjectValue<LegacyProtocolMappings>(createLegacyProtocolMappingsFromDiscriminatorValue); },
        "solutionDefinition": n => { legacyProtocolOptions.solutionDefinition = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoLifecycle(lifecycle: Partial<Lifecycle> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "fulfillmentRequired": n => { lifecycle.fulfillmentRequired = n.getBooleanValue(); },
        "instanceMode": n => { lifecycle.instanceMode = n.getStringValue(); },
        "protocol": n => { lifecycle.protocol = n.getStringValue(); },
        "protocolOptions": n => { lifecycle.protocolOptions = n.getObjectValue<LegacyProtocolOptions>(createLegacyProtocolOptionsFromDiscriminatorValue) ?? n.getObjectValue<WebhookProtocolOptions>(createWebhookProtocolOptionsFromDiscriminatorValue); },
        "providerId": n => { lifecycle.providerId = n.getStringValue(); },
        "trigger": n => { lifecycle.trigger = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoProtocolOptions(protocolOptions: Partial<LegacyProtocolOptions | WebhookProtocolOptions> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        ...deserializeIntoLegacyProtocolOptions(protocolOptions as LegacyProtocolOptions),
        ...deserializeIntoWebhookProtocolOptions(protocolOptions as WebhookProtocolOptions),
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoServiceAvailability(serviceAvailability: Partial<ServiceAvailability> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "enabled": n => { serviceAvailability.enabled = n.getBooleanValue(); },
        "limit": n => { serviceAvailability.limit = n.getNumberValue(); },
        "visible": n => { serviceAvailability.visible = n.getBooleanValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoServiceCollectionResponse(serviceCollectionResponse: Partial<ServiceCollectionResponse> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "items": n => { serviceCollectionResponse.items = n.getCollectionOfObjectValues<ServiceResponse>(createServiceResponseFromDiscriminatorValue); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoServiceResponse(serviceResponse: Partial<ServiceResponse> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "applications": n => { serviceResponse.applications = n.getCollectionOfObjectValues<Application>(createApplicationFromDiscriminatorValue); },
        "availability": n => { serviceResponse.availability = n.getObjectValue<ServiceAvailability>(createServiceAvailabilityFromDiscriminatorValue); },
        "category": n => { serviceResponse.category = n.getEnumValue<CategoryNullable>(CategoryNullableObject); },
        "dependencies": n => { serviceResponse.dependencies = n.getObjectValue<ServiceResponse_dependencies>(createServiceResponse_dependenciesFromDiscriminatorValue); },
        "description": n => { serviceResponse.description = n.getStringValue(); },
        "displayName": n => { serviceResponse.displayName = n.getStringValue(); },
        "externalIdentities": n => { serviceResponse.externalIdentities = n.getCollectionOfObjectValues<ExternalIdentity>(createExternalIdentityFromDiscriminatorValue); },
        "geographies": n => { serviceResponse.geographies = n.getCollectionOfObjectValues<Geography>(createGeographyFromDiscriminatorValue); },
        "hostingType": n => { serviceResponse.hostingType = n.getStringValue(); },
        "iconUrl": n => { serviceResponse.iconUrl = n.getStringValue(); },
        "id": n => { serviceResponse.id = n.getStringValue(); },
        "lifecycle": n => { serviceResponse.lifecycle = n.getObjectValue<Lifecycle>(createLifecycleFromDiscriminatorValue); },
        "tags": n => { serviceResponse.tags = n.getCollectionOfPrimitiveValues<string>(); },
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoServiceResponse_dependencies(serviceResponse_dependencies: Partial<ServiceResponse_dependencies> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
    }
}
/**
 * The deserialization information for the current model
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoWebhookProtocolOptions(webhookProtocolOptions: Partial<WebhookProtocolOptions> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "webhookUri": n => { webhookProtocolOptions.webhookUri = n.getStringValue(); },
    }
}
/**
 * Represents an external identity that can be associated with a service. External identities provide integration points with third-party authentication and authorization systems.
 */
export interface ExternalIdentity extends Parsable {
    /**
     * The unique identifier for this external identity. This ID is used to reference this specific identity in other operations.
     */
    id?: string | null;
    /**
     * The authorization scopes associated with this external identity. Each scope represents a specific permission or access level granted to the identity.
     */
    scopes?: ExternalIdentityScope[] | null;
    /**
     * Defines the supported external identity providers that can be used for authentication and authorization with services.
     */
    type?: ExternalIdentityType | null;
}
export type ExternalIdentityScope = (typeof ExternalIdentityScopeObject)[keyof typeof ExternalIdentityScopeObject];
export type ExternalIdentityType = (typeof ExternalIdentityTypeObject)[keyof typeof ExternalIdentityTypeObject];
/**
 * Defines the catalog service allowed geographies. Geographies represent regions where a service can be deployed and accessed.
 */
export interface Geography extends Parsable {
    /**
     * The geography identifier. This unique code represents a specific geographic region where a service can be deployed and accessed.
     */
    id?: string | null;
}
/**
 * Defines the mapping of an application to a legacy Solution and Capability Management (SCM) capability definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
 */
export interface LegacyProtocolApplicationMapping extends Parsable {
    /**
     * The name of the legacy SCM capability definition to which the application corresponds. This identifier is used by legacy systems to recognize and interact with the application.
     */
    capabilityDefinition?: string | null;
    /**
     * The name of the application to be mapped. This must match an application name defined in the service's application collection.
     */
    name?: string | null;
}
/**
 * Defines the mapping between a service and corresponding legacy Solution and Capability Management (SCM) solution definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
 */
export interface LegacyProtocolMappings extends Parsable {
    /**
     * The mappings of applications to SCM capability definitions. Each entry defines how an application in the current system relates to a capability definition in the legacy SCM system.
     */
    applications?: LegacyProtocolApplicationMapping[] | null;
    /**
     * The mapping of the service dependencies to legacy SCM integration definitions. Keys represent dependency identifiers in the current system, and values contain the mapping details for connecting to the legacy SCM integration system.
     */
    dependencies?: LegacyProtocolMappings_dependencies | null;
    /**
     * The mapping of geographies to the legacy declared SCM solution regions. Keys represent geography IDs in the current system, and values represent the corresponding region identifiers in the legacy SCM system.
     */
    geographies?: LegacyProtocolMappings_geographies | null;
}
/**
 * The mapping of the service dependencies to legacy SCM integration definitions. Keys represent dependency identifiers in the current system, and values contain the mapping details for connecting to the legacy SCM integration system.
 */
export interface LegacyProtocolMappings_dependencies extends AdditionalDataHolder, Parsable {
    /**
     * Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.
     */
    additionalData?: Record<string, unknown>;
}
/**
 * The mapping of geographies to the legacy declared SCM solution regions. Keys represent geography IDs in the current system, and values represent the corresponding region identifiers in the legacy SCM system.
 */
export interface LegacyProtocolMappings_geographies extends AdditionalDataHolder, Parsable {
    /**
     * Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.
     */
    additionalData?: Record<string, unknown>;
}
/**
 * Defines how the legacy Solution and Capability Management (SCM) protocol options correspond to a catalog service. These options enable backward compatibility with systems that use the older SCM protocol.
 */
export interface LegacyProtocolOptions extends Parsable {
    /**
     * Defines the mapping between a service and corresponding legacy Solution and Capability Management (SCM) solution definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
     */
    mappings?: LegacyProtocolMappings | null;
    /**
     * The legacy SCM solution definition to which this service corresponds. This identifier links the catalog service to its equivalent representation in the legacy SCM system.
     */
    solutionDefinition?: string | null;
}
/**
 * Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns.
 */
export interface Lifecycle extends Parsable {
    /**
     * Whether the service requires manual input from Aveva Operators.
     */
    fulfillmentRequired?: boolean | null;
    /**
     * Defines the resource allocation strategy for service instances. Options include:            `Shared` (multiple customers share the same underlying resources with logical separation),            `Isolated` (dedicated resources are provisioned specifically for each customer instance).
     */
    instanceMode?: string | null;
    /**
     * Specifies the communication pattern used for lifecycle events. The default value is `IntegrationEvent`, which implements a publish-subscribe pattern where instance management publishes lifecycle events and waits for acknowledgment from the service provider.
     */
    protocol?: string | null;
    /**
     * The protocol options. The actual type is determined by the Protocol property. When Protocol is `Legacy`, this will be a LegacyProtocolOptions. When Protocol is `Webhook`, this will be a WebhookProtocolOptions.
     */
    protocolOptions?: LegacyProtocolOptions | WebhookProtocolOptions | null;
    /**
     * Identifies the service provider responsible for handling lifecycle events. The provider implements the service-specific logic needed to create, configure, and manage service instances according to customer requirements.
     */
    providerId?: string | null;
    /**
     * Determines when and how service instances are provisioned. Valid values include:            `None` (service does not require provisioning),            `Account` (automatically provisioned when an account is created),            `Catalog` (provisioned on-demand by customer request).
     */
    trigger?: string | null;
}
export type ProtocolOptions = LegacyProtocolOptions | WebhookProtocolOptions;
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeApplication(writer: SerializationWriter, application: Partial<Application> | undefined | null = {}) : void {
    if (application) {
        writer.writeStringValue("name", application.name);
        writer.writeObjectValue<Application_urls>("urls", application.urls, serializeApplication_urls);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeApplication_urls(writer: SerializationWriter, application_urls: Partial<Application_urls> | undefined | null = {}) : void {
    if (application_urls) {
        writer.writeAdditionalData(application_urls.additionalData);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeExternalIdentity(writer: SerializationWriter, externalIdentity: Partial<ExternalIdentity> | undefined | null = {}) : void {
    if (externalIdentity) {
        writer.writeStringValue("id", externalIdentity.id);
        if(externalIdentity.scopes)
        writer.writeCollectionOfEnumValues<ExternalIdentityScope>("scopes", externalIdentity.scopes);
        writer.writeEnumValue<ExternalIdentityType>("type", externalIdentity.type);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeGeography(writer: SerializationWriter, geography: Partial<Geography> | undefined | null = {}) : void {
    if (geography) {
        writer.writeStringValue("id", geography.id);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeLegacyProtocolApplicationMapping(writer: SerializationWriter, legacyProtocolApplicationMapping: Partial<LegacyProtocolApplicationMapping> | undefined | null = {}) : void {
    if (legacyProtocolApplicationMapping) {
        writer.writeStringValue("capabilityDefinition", legacyProtocolApplicationMapping.capabilityDefinition);
        writer.writeStringValue("name", legacyProtocolApplicationMapping.name);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeLegacyProtocolMappings(writer: SerializationWriter, legacyProtocolMappings: Partial<LegacyProtocolMappings> | undefined | null = {}) : void {
    if (legacyProtocolMappings) {
        writer.writeCollectionOfObjectValues<LegacyProtocolApplicationMapping>("applications", legacyProtocolMappings.applications, serializeLegacyProtocolApplicationMapping);
        writer.writeObjectValue<LegacyProtocolMappings_dependencies>("dependencies", legacyProtocolMappings.dependencies, serializeLegacyProtocolMappings_dependencies);
        writer.writeObjectValue<LegacyProtocolMappings_geographies>("geographies", legacyProtocolMappings.geographies, serializeLegacyProtocolMappings_geographies);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeLegacyProtocolMappings_dependencies(writer: SerializationWriter, legacyProtocolMappings_dependencies: Partial<LegacyProtocolMappings_dependencies> | undefined | null = {}) : void {
    if (legacyProtocolMappings_dependencies) {
        writer.writeAdditionalData(legacyProtocolMappings_dependencies.additionalData);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeLegacyProtocolMappings_geographies(writer: SerializationWriter, legacyProtocolMappings_geographies: Partial<LegacyProtocolMappings_geographies> | undefined | null = {}) : void {
    if (legacyProtocolMappings_geographies) {
        writer.writeAdditionalData(legacyProtocolMappings_geographies.additionalData);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeLegacyProtocolOptions(writer: SerializationWriter, legacyProtocolOptions: Partial<LegacyProtocolOptions> | undefined | null = {}) : void {
    if (legacyProtocolOptions) {
        writer.writeObjectValue<LegacyProtocolMappings>("mappings", legacyProtocolOptions.mappings, serializeLegacyProtocolMappings);
        writer.writeStringValue("solutionDefinition", legacyProtocolOptions.solutionDefinition);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeLifecycle(writer: SerializationWriter, lifecycle: Partial<Lifecycle> | undefined | null = {}) : void {
    if (lifecycle) {
        writer.writeBooleanValue("fulfillmentRequired", lifecycle.fulfillmentRequired);
        writer.writeStringValue("instanceMode", lifecycle.instanceMode);
        writer.writeStringValue("protocol", lifecycle.protocol);
        writer.writeObjectValue<LegacyProtocolOptions | WebhookProtocolOptions>("protocolOptions", lifecycle.protocolOptions, serializeProtocolOptions);
        writer.writeStringValue("providerId", lifecycle.providerId);
        writer.writeStringValue("trigger", lifecycle.trigger);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeProtocolOptions(writer: SerializationWriter, protocolOptions: Partial<LegacyProtocolOptions | WebhookProtocolOptions> | undefined | null = {}) : void {
    serializeLegacyProtocolOptions(writer, protocolOptions as LegacyProtocolOptions);
    serializeWebhookProtocolOptions(writer, protocolOptions as WebhookProtocolOptions);
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeServiceAvailability(writer: SerializationWriter, serviceAvailability: Partial<ServiceAvailability> | undefined | null = {}) : void {
    if (serviceAvailability) {
        writer.writeBooleanValue("enabled", serviceAvailability.enabled);
        writer.writeNumberValue("limit", serviceAvailability.limit);
        writer.writeBooleanValue("visible", serviceAvailability.visible);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeServiceCollectionResponse(writer: SerializationWriter, serviceCollectionResponse: Partial<ServiceCollectionResponse> | undefined | null = {}) : void {
    if (serviceCollectionResponse) {
        writer.writeCollectionOfObjectValues<ServiceResponse>("items", serviceCollectionResponse.items, serializeServiceResponse);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeServiceResponse(writer: SerializationWriter, serviceResponse: Partial<ServiceResponse> | undefined | null = {}) : void {
    if (serviceResponse) {
        writer.writeCollectionOfObjectValues<Application>("applications", serviceResponse.applications, serializeApplication);
        writer.writeObjectValue<ServiceAvailability>("availability", serviceResponse.availability, serializeServiceAvailability);
        writer.writeEnumValue<CategoryNullable>("category", serviceResponse.category);
        writer.writeObjectValue<ServiceResponse_dependencies>("dependencies", serviceResponse.dependencies, serializeServiceResponse_dependencies);
        writer.writeStringValue("description", serviceResponse.description);
        writer.writeStringValue("displayName", serviceResponse.displayName);
        writer.writeCollectionOfObjectValues<ExternalIdentity>("externalIdentities", serviceResponse.externalIdentities, serializeExternalIdentity);
        writer.writeCollectionOfObjectValues<Geography>("geographies", serviceResponse.geographies, serializeGeography);
        writer.writeStringValue("hostingType", serviceResponse.hostingType);
        writer.writeStringValue("iconUrl", serviceResponse.iconUrl);
        writer.writeStringValue("id", serviceResponse.id);
        writer.writeObjectValue<Lifecycle>("lifecycle", serviceResponse.lifecycle, serializeLifecycle);
        writer.writeCollectionOfPrimitiveValues<string>("tags", serviceResponse.tags);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeServiceResponse_dependencies(writer: SerializationWriter, serviceResponse_dependencies: Partial<ServiceResponse_dependencies> | undefined | null = {}) : void {
    if (serviceResponse_dependencies) {
        writer.writeAdditionalData(serviceResponse_dependencies.additionalData);
    }
}
/**
 * Serializes information the current object
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeWebhookProtocolOptions(writer: SerializationWriter, webhookProtocolOptions: Partial<WebhookProtocolOptions> | undefined | null = {}) : void {
    if (webhookProtocolOptions) {
        writer.writeStringValue("webhookUri", webhookProtocolOptions.webhookUri);
    }
}
/**
 * Defines provisioning availability and constraints for a service. This resource contains information about how a service can be discovered and provisioned to an account.
 */
export interface ServiceAvailability extends Parsable {
    /**
     * Indicates whether the service is visible in the service catalog (`true`) or hidden (`false`).            When set to `true`, the service appears in catalog listings and can be discovered by users.            When set to `false`, the service is hidden from catalog views but may still be available through direct access.
     */
    enabled?: boolean | null;
    /**
     * Specifies the maximum number of instances an account can have of this service.            A value of `null` indicates the default limit (10) is applied.            A value of `0` indicates the service cannot be provisioned.            Any positive integer represents the maximum number of instances allowed.
     */
    limit?: number | null;
    /**
     * Indicates whether an approval workflow is required (`true`) or if the service can be provisioned immediately (`false`).            When set to `true`, service provisioning must go through a request and approval process.            When set to `false`, accounts can provision the service immediately without approval.
     * @deprecated 
     */
    visible?: boolean | null;
}
/**
 * Represents a collection of service catalog entries returned by the API.
 */
export interface ServiceCollectionResponse extends Parsable {
    /**
     * The list of catalog services with their detailed information and configurations. Each item contains complete service details including identity, dependencies, availability settings, and lifecycle configuration.
     */
    items?: ServiceResponse[] | null;
}
/**
 * Represents a catalog service entry with detailed information about the service and its capabilities.
 */
export interface ServiceResponse extends Parsable {
    /**
     * Applications associated with this service. Each application represents a discrete component or interface that provides specific functionality within the service.
     */
    applications?: Application[] | null;
    /**
     * Defines provisioning availability and constraints for a service. This resource contains information about how a service can be discovered and provisioned to an account.
     */
    availability?: ServiceAvailability | null;
    /**
     * The category property
     */
    category?: CategoryNullable | null;
    /**
     * Other services that this service depends on to function properly. Keys represent the dependency identifier, and values contain details about the dependency relationship including cardinality and configuration requirements.
     */
    dependencies?: ServiceResponse_dependencies | null;
    /**
     * A detailed description of the service explaining its purpose, capabilities, and key features. This text helps users understand what the service does and when to use it.
     */
    description?: string | null;
    /**
     * The user-friendly name of the service displayed in user interfaces. This is the primary label used to identify the service to end users.
     */
    displayName?: string | null;
    /**
     * External identity providers associated with this service. These identities enable integration with third-party authentication and authorization systems.
     */
    externalIdentities?: ExternalIdentity[] | null;
    /**
     * Geographic regions where this service can be provisioned. Only applicable and required for services with `External` hosting type. Each geography represents a distinct region where the service can be deployed and accessed. For non-external hosting types, this collection should be empty.
     */
    geographies?: Geography[] | null;
    /**
     * The deployment scope of the service. Valid values include:            `Environment` (available in all regions and geographies),            `Geography` (available in all regions within a specific geography),            `Regional` (available in a specific region within a specific geography),            `External` (hosted outside the platform but accessible through platform integration).
     */
    hostingType?: string | null;
    /**
     * A URL pointing to the service's icon image stored in the AVEVA CDN. When using this URL, implement proper cache control to ensure users see updated icons when they change. Browsers should be configured to periodically check for updates rather than caching indefinitely.
     */
    iconUrl?: string | null;
    /**
     * The unique identifier for this service. This ID is used to reference the service in other operations.
     */
    id?: string | null;
    /**
     * Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns.
     */
    lifecycle?: Lifecycle | null;
    /**
     * Terms used to categorize and filter the service. Tags provide additional classification beyond the primary category and help users discover related services.
     */
    tags?: string[] | null;
}
/**
 * Other services that this service depends on to function properly. Keys represent the dependency identifier, and values contain details about the dependency relationship including cardinality and configuration requirements.
 */
export interface ServiceResponse_dependencies extends AdditionalDataHolder, Parsable {
    /**
     * Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.
     */
    additionalData?: Record<string, unknown>;
}
/**
 * Defines the webhook protocol options for a catalog service.
 */
export interface WebhookProtocolOptions extends Parsable {
    /**
     * The webhook URI for the Webhook Protocol.
     */
    webhookUri?: string | null;
}
export const CategoryNullableObject = {
    Data: "Data",
    Ingress: "Ingress",
} as const;
/**
 * Defines the authorization scope types available for external identities. Each scope represents a specific permission level or access role.
 */
export const ExternalIdentityScopeObject = {
    ApiRole: "apiRole",
    OpsRole: "opsRole",
} as const;
/**
 * Defines the supported external identity providers that can be used for authentication and authorization with services.
 */
export const ExternalIdentityTypeObject = {
    AvevaRnDEntraID: "AvevaRnDEntraID",
} as const;
/* tslint:enable */
/* eslint-enable */
