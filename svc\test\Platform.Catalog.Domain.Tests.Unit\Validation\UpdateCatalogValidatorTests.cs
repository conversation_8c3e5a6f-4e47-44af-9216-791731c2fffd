﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Validation;
using FluentValidation;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Validation;

/// <summary>
/// <see cref="UpdateCatalogValidator"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public class UpdateCatalogValidatorTests
{
    public UpdateCatalogValidatorTests()
    {
    }

    #region Test Cases

    [Fact]
    public async Task UpdateCatalogValidator_ValidateDependenciesExist_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>()
            {
                {
                    "dependency", new V1CatalogDataDependency()
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        var subject = new UpdateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateDependenciesExist_Throws()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency2", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        var subject = new UpdateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Dependant Service entry dependency2 is not found.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateDependenciesExist_Skip_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency2", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        var subject = new UpdateCatalogValidator(oldEntries, true);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateDependencies_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Colocated = true,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        var subject = new UpdateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateDependencies_Config_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Colocated = true,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 10, Max = 50 } },
                            { "config2", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = null, Max = null } },
                            { "config3", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 10, Max = null } },
                            { "config4", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = null, Max = 50 } },
                        },
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        var subject = new UpdateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateDependencies_Config_Throws()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Colocated = true,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            { "config1", new V1CatalogDataDependencyConfig { Label = string.Empty, Help = "help1", Min = -1, Max = 10 } },
                            { "config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = string.Empty, Min = 50, Max = 10 } },
                        },
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        var subject = new UpdateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(4);
        result.Errors.Exists(e => e.PropertyName.Equals("Label") && e.ErrorMessage.Equals(
            "Required for the dependency: dependency config: config1",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.PropertyName.Equals("Min") && e.ErrorMessage.Equals(
            "Must be positive for the dependency: dependency config: config1",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.PropertyName.Equals("Help") && e.ErrorMessage.Equals(
            "Required for the dependency: dependency config: config2",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.PropertyName.Equals("Max") && e.ErrorMessage.Equals(
            "Must be greater than or equal to min for the dependency: dependency config: config2",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_Validation_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "dependency",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
            Dependencies = new Dictionary<string, Dependency>()
            {
                {
                    "dependency1", new Dependency()
                    {
                        Type = DependencyType.Optional,
                        Cardinality = DependencyCardinality.One,
                    }
                },
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>()
            {
                {
                    "dependency", new V1CatalogDataDependency()
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.Many,
                    }
                },
            },
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ExternalIdentityWithEmptyScope_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "name",
            Category = Category.Data,
            HostingType = "Environment",
            Description = "description",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "name",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            ExternalIdentities =
            [
                new V1ExternalIdentity()
                {
                    Id = "guid1",
                    Scopes = [],
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe($"{nameof(V1ExternalIdentity.Scopes)} cannot be empty for 'guid1' identity.");
    }

    [Fact]
    public async Task UpdateCatalogValidator_ExternalIdentityWithDuplicateScope_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "name",
            Category = Category.Data,
            HostingType = "Environment",
            Description = "description",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "name",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            ExternalIdentities =
            [
                new V1ExternalIdentity()
                {
                    Id = "guid1",
                    Scopes = [V1Scope.opsRole, V1Scope.opsRole],
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe($"{nameof(V1ExternalIdentity.Scopes)} cannot contain duplicate scopes for 'guid1' identity.");
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateApplication_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Catalog.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications = new List<V1Application>()
            {
                new()
                {
                    Name = "testName",
                },
            },
            Availability = new V1ServiceAvailability()
            {
                Limit = 4,
                Enabled = true,
            },

            ExternalIdentities =
            [
                new V1ExternalIdentity()
                {
                    Id = "guid1",
                    Scopes = [V1Scope.opsRole],
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLifecycle_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications = new List<V1Application>()
            {
                new()
                {
                    Name = "testName",
                },
            },
            Availability = new V1ServiceAvailability()
            {
                Limit = 4,
                Enabled = true,
            },
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "ServiceEntry Availability cannot be set if the lifecycle trigger is not catalog.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateApplicationDefaultUrl_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications = new List<V1Application>()
            {
                new()
                {
                    Name = "testName",
                    Urls = new Dictionary<string, string>()
                    {
                        { "default", "url1" },
                    },
                },
            },
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateApplicationUrls_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications = new List<V1Application>()
            {
                new()
                {
                    Name = "testName",
                    Urls = new Dictionary<string, string>()
                    {
                        { "default", "url1" },
                        { "eu", "url" },
                    },
                },
            },
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateApplicationNoDefaultUrl_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications = new List<V1Application>()
            {
                new()
                {
                    Name = "testName",
                    Urls = new Dictionary<string, string>()
                    {
                        { "eu", "url" },
                    },
                },
            },
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = new List<ServiceResponse>() { oldEntry } };
        UpdateCatalogValidator subject = new UpdateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Application testName urls must also contain a default entry.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLegacyProtcol_WhenProtocolOptionsNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "ProtocolOptions required for the Legacy Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLegacyProtocol_WhenMappingsGeographiesDoesntContainDefaultKey_ReturnsValidationFailure()
    {
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "SolutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string> { },
                    },
                },
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "You must have at least a 'default' geography mapping",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLegacyProtocol_WhenMappingsNull_ReturnsValidationFailure()
    {
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = null,
                },
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Mappings required for the Legacy Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLegacyProtcol_WhenMappingsGeographiesNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = null,
                    },
                },
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "You must have geographies and at least a 'default' geography mapping",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLegacyProtcol_WhenMappingsGeographiesDoesntContainDefaultKey_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>(),
                    },
                },
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "You must have at least a 'default' geography mapping",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateLegacyProtocol_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                             {
                                 { "default", "region1" },
                             },
                    },
                },
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateProtocolOptions_WhenProtocolOptionsNull_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.IntegrationEvent,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };
        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateWebhookProtocol_WhenProtocolOptionsNull_ReturnsValidationFailure()
    {
        // Arrange
        var oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.Webhook.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };

        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse { Items = new List<ServiceResponse> { oldEntry } }, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(newEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "ProtocolOptions required for the Webhook Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateWebhookProtocol_WhenWebhookUriNull_ReturnsValidationFailure()
    {
        // Arrange
        var oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.Webhook.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = null!,
                },
                false),
        };

        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse { Items = new List<ServiceResponse> { oldEntry } }, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(newEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "WebhookUri required for the Webhook Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateWebhookProtocol_WhenWebhookUriRelative_ReturnsValidationFailure()
    {
        // Arrange
        var oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.Webhook.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("relative/path", UriKind.Relative),
                },
                false),
        };

        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse { Items = new List<ServiceResponse> { oldEntry } }, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(newEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "WebhookUri must be a valid absolute URI",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task UpdateCatalogValidator_ValidateWebhookProtocol_WhenWebhookUriValid_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.Webhook.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://example.com/new-webhook"),
                },
                false),
        };

        var subject = new UpdateCatalogValidator(new ServiceCollectionResponse { Items = new List<ServiceResponse> { oldEntry } }, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(newEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    #endregion Test Cases
}