﻿namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common
{
    public class DaprCloudEvent
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public CatalogServiceEntryEvent Data { get; set; }
        public string Topic { get; set; }
        public string Source { get; set; }
        public string Id { get; set; }
    }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
}