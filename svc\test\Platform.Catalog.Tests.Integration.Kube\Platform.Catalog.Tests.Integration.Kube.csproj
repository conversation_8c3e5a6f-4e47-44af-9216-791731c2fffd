﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Tests.Integration.Kube</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
    <OutputType>exe</OutputType>
  </PropertyGroup>

  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  
  <PropertyGroup>
    <UserSecretsId>catalog-int-test-8D528ECA-AEF2-4732-AC21-FBB29348BC62</UserSecretsId>
  </PropertyGroup>
  
  <!-- If modifying the test assets, the folder needs to be cleaned in the bin directory-->
  <ItemGroup>
    <Content Include="TestAssets\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
        
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Messaging.EventBus" />
    <PackageReference Include="Aveva.Platform.Common.Messaging.EventBus.Events" />
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.msbuild">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
	  <PackageReference Include="KubernetesClient" />
	  <PackageReference Include="Microsoft.Extensions.Configuration" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" />
	  <PackageReference Include="Microsoft.Extensions.Logging.Console" />
	  <PackageReference Include="Microsoft.NET.Test.Sdk" />
	  <PackageReference Include="Polly" />
	  <PackageReference Include="RabbitMQ.Client" />
	  <PackageReference Include="xunit.v3" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Shouldly" />
  </ItemGroup>
        
  <ItemGroup>
    <ProjectReference Include="..\..\src\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
    <ProjectReference Include="..\..\src\Platform.Catalog.Infrastructure\Platform.Catalog.Infrastructure.csproj" />
  </ItemGroup>
        
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>
