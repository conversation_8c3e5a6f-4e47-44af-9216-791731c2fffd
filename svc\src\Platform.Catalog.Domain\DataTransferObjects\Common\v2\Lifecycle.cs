﻿using System.Text.Json.Serialization;
using Aveva.Platform.Catalog.Domain.Serialization;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns.
    /// </summary>
    [JsonConverter(typeof(LifecycleJsonConverter))]
    public class Lifecycle
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Lifecycle"/> class.
        /// </summary>
#pragma warning disable CS8618 // Required for serialization
        public Lifecycle()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Lifecycle"/> class.
        /// </summary>
        public Lifecycle(string trigger, string? protocol, string providerId, string? instanceMode, ProtocolOptions? protocolOptions = null)
        {
            Trigger = trigger;
            Protocol = protocol;
            ProviderId = providerId;
            InstanceMode = instanceMode;
            ProtocolOptions = protocolOptions;
        }

        /// <summary>
        /// Determines when and how service instances are provisioned. Valid values include:
        /// `None` (service does not require provisioning),
        /// `Account` (automatically provisioned when an account is created),
        /// `Catalog` (provisioned on-demand by customer request).
        /// </summary>
        public string Trigger { get; set; }

        /// <summary>
        /// Specifies the communication pattern used for lifecycle events. The default value is `IntegrationEvent`, which implements a publish-subscribe pattern where instance management publishes lifecycle events and waits for acknowledgment from the service provider.
        /// </summary>
        public string? Protocol { get; set; }

        /// <summary>
        /// Identifies the service provider responsible for handling lifecycle events. The provider implements the service-specific logic needed to create, configure, and manage service instances according to customer requirements.
        /// </summary>
        public string ProviderId { get; set; }

        /// <summary>
        /// Defines the resource allocation strategy for service instances. Options include:
        /// `Shared` (multiple customers share the same underlying resources with logical separation),
        /// `Isolated` (dedicated resources are provisioned specifically for each customer instance).
        /// </summary>
        public string? InstanceMode { get; set; }

        /// <summary>
        /// Contains protocol-specific configuration options, including any mappings needed for backward compatibility with legacy systems or provide support for webhook.
        /// These options control how the lifecycle protocol interacts with other systems during provisioning operations.
        /// For Legacy protocol, this will be a <see cref="LegacyProtocolOptions"/>.
        /// For Webhook protocol, this will be a <see cref="WebhookProtocolOptions"/>.
        /// Contains protocol-specific configuration options, including any mappings needed for backward compatibility with legacy systems. These options control how the lifecycle protocol interacts with other systems during provisioning operations.
        /// </summary>
        public ProtocolOptions? ProtocolOptions { get; set; }

        /// <summary>
        /// Whether the service requires manual input from Aveva Operators.
        /// </summary>
        public bool FulfillmentRequired { get; set; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is Lifecycle item
                && string.Equals(item.Trigger.ToString(), Trigger.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Protocol, Protocol, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.ProviderId?.ToString(), ProviderId?.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.InstanceMode, InstanceMode, StringComparison.InvariantCultureIgnoreCase)
                && item.FulfillmentRequired.Equals(FulfillmentRequired)
                && ((item.ProtocolOptions == null && ProtocolOptions == null) || (item.ProtocolOptions != null && item.ProtocolOptions.Equals(ProtocolOptions)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}