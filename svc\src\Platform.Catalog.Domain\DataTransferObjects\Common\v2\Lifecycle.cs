using System.Text.Json.Serialization;
using Aveva.Platform.Catalog.Domain.Serialization;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns.
    /// </summary>
    [JsonConverter(typeof(LifecycleJsonConverter))]
    public class Lifecycle
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Lifecycle"/> class.
        /// </summary>
#pragma warning disable CS8618 // Required for serialization
        public Lifecycle()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Lifecycle"/> class.
        /// </summary>
        public Lifecycle(string trigger, string? protocol, string providerId, string? instanceMode, ProtocolOptions? protocolOptions = null)
        {
            Trigger = trigger;
            Protocol = protocol;
            ProviderId = providerId;
            InstanceMode = instanceMode;
            ProtocolOptions = protocolOptions;
        }

        /// <summary>
        /// Determines when and how service instances are provisioned. Valid values include None (service does not require provisioning), Account (automatically provisioned when an account is created), and Catalog (provisioned on-demand by customer request).
        /// </summary>
        public string Trigger { get; set; }

        /// <summary>
        /// The communication protocol used for lifecycle events. The default value is IntegrationEvent.
        /// </summary>
        public string? Protocol { get; set; }

        /// <summary>
        /// The service provider responsible for handling lifecycle events and managing service instances.
        /// </summary>
        public string ProviderId { get; set; }

        /// <summary>
        /// The resource allocation strategy for service instances. Options include Shared (multiple customers share resources with logical separation) and Isolated (dedicated resources for each customer).
        /// </summary>
        public string? InstanceMode { get; set; }

        /// <summary>
        /// Protocol-specific configuration options that control how the lifecycle protocol interacts with other systems during provisioning operations. Includes mappings for backward compatibility with legacy systems and webhook support.
        /// For Legacy protocol, this will be a <see cref="LegacyProtocolOptions"/>.
        /// For Webhook protocol, this will be a <see cref="WebhookProtocolOptions"/>.
        /// </summary>
        public ProtocolOptions? ProtocolOptions { get; set; }

        /// <summary>
        /// Indicates whether the service requires manual input from AVEVA operators.
        /// </summary>
        public bool FulfillmentRequired { get; set; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is Lifecycle item
                && string.Equals(item.Trigger.ToString(), Trigger.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Protocol, Protocol, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.ProviderId?.ToString(), ProviderId?.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.InstanceMode, InstanceMode, StringComparison.InvariantCultureIgnoreCase)
                && item.FulfillmentRequired.Equals(FulfillmentRequired)
                && ((item.ProtocolOptions == null && ProtocolOptions == null) || (item.ProtocolOptions != null && item.ProtocolOptions.Equals(ProtocolOptions)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}