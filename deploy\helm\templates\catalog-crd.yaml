apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: serviceentries.servicecatalog.aveva.com
spec:
  group: servicecatalog.aveva.com
  names:
    kind: ServiceEntry
    listKind: ServiceEntryList
    plural: serviceentries
    singular: serviceentry
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            nullable: false
            properties:
              id:
                nullable: false
                type: string
              iconUrl:
                nullable: true
                type: string
              description:
                nullable: true
                type: string
              displayName:
                nullable: false
                type: string
              category:
                enum:
                - Data
                - Ingress
                nullable: true
                type: string
              hostingType:
                enum:
                - Environment
                - Geography
                - Regional
                - External
                nullable: false
                type: string
              availability:
                nullable: true
                type: object
                properties:
                  limit:
                    format: int32
                    nullable: true
                    type: integer
                  enabled:
                    nullable: true
                    type: boolean
              tags:
                items:
                  nullable: false
                  type: string
                nullable: true
                type: array
              lifecycle:
                nullable: false
                properties:
                  trigger:
                    enum:
                    - None
                    - Account
                    - Catalog
                    nullable: false
                    type: string
                  protocol:
                    enum:
                    - IntegrationEvent
                    - Legacy
                    - Webhook
                    nullable: true
                    type: string
                  providerId:
                    nullable: true
                    type: string
                  instanceMode:
                    enum:
                    - Shared
                    - Isolated
                    nullable: true
                    type: string
                  protocolOptions:
                    nullable: true
                    properties:
                      solutionDefinition:
                        nullable: true
                        type: string
                      mappings:
                        nullable: true
                        properties:
                          dependencies:
                            additionalProperties:
                              properties:
                                integrationDefinition:
                                  nullable: false
                                  type: string
                                sourceContextConfig:
                                  nullable: true
                                  type: string
                                targetContextConfig:
                                  nullable: true
                                  type: string
                              type: object
                            nullable: true
                            type: object
                          applications:
                            items:
                              properties:
                                name:
                                  nullable: false
                                  type: string
                                capabilityDefinition:
                                  nullable: false
                                  type: string
                              required:
                              - name
                              - capabilityDefinition
                              type: object
                            nullable: true
                            type: array
                          geographies:
                            additionalProperties:
                              nullable: false
                              type: string
                            nullable: true
                            type: object
                        type: object
                      webhookUri:
                        nullable: true
                        type: string
                    type: object
                  fulfillmentRequired:
                    nullable: false
                    type: boolean
                required:
                - trigger
                type: object
              dependencies:
                additionalProperties:
                  properties:
                    type:
                      enum:
                      - Optional
                      - Required
                      nullable: false
                      type: string
                    cardinality:
                      enum:
                      - One
                      - Many
                      nullable: false
                      type: string
                    colocated:
                      nullable: true
                      type: boolean
                    config:
                      additionalProperties:
                        properties:
                          label:
                            nullable: false
                            type: string
                          help:
                            nullable: false
                            type: string
                          required:
                            nullable: false
                            type: boolean
                          min:
                            format: int32
                            nullable: true
                            type: integer
                          max:
                            format: int32
                            nullable: true
                            type: integer
                        required:
                        - label
                        - help
                        type: object
                      nullable: true
                      type: object
                  required:
                  - type
                  - cardinality
                  type: object
                nullable: true
                type: object
              applications:
                items:
                  properties:
                    name:
                      nullable: false
                      type: string
                    urls:
                      additionalProperties:
                        nullable: false
                        type: string
                      nullable: true
                      type: object
                  required:
                  - name
                  type: object
                nullable: true
                type: array
              geographies:
                items:
                  properties:
                    id:
                      nullable: false
                      type: string
                  required:
                  - id
                  type: object
                nullable: true
                type: array
              externalIdentities:
                items:
                  properties:
                    id:
                      nullable: false
                      type: string
                    type:
                      enum:
                        - AvevaRnDEntraID
                      nullable: false
                      type: string
                    scopes:
                      nullable: false
                      type: array
                      items: 
                        enum:
                          - apiRole
                          - opsRole
                        type: string
                  required:
                  - id
                  - type
                  - scopes
                  type: object
                nullable: true
                type: array
            required:
            - id
            - displayName
            - hostingType
            - lifecycle
            type: object
        type: object
    served: true
    storage: true