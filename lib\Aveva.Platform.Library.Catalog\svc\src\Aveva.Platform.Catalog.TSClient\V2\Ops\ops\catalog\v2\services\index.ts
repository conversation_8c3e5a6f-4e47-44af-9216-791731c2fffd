/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { CategoryNullable, createServiceCollectionResponseFromDiscriminatorValue, type ServiceCollectionResponse } from '../../../../models/index.js';
// @ts-ignore
import { ServicesItemRequestBuilderRequestsMetadata, type ServicesItemRequestBuilder } from './item/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata, type Parsable, type ParsableFactory, type RequestConfiguration, type RequestInformation, type RequestsMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /ops/catalog/v2/services
 */
export interface ServicesRequestBuilder extends BaseRequestBuilder<ServicesRequestBuilder> {
    /**
     * Gets an item from the Aveva.Platform.Catalog.Client.V2.Ops.ops.catalog.v2.services.item collection
     * @param id The unique identifier for this service. This ID is used to reference the service in other operations.
     * @returns {ServicesItemRequestBuilder}
     */
     byId(id: string) : ServicesItemRequestBuilder;
    /**
     * Gets a collection of all catalog service entries. This operations endpoint allows viewing all services regardless of their availability to specific accounts.
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {Promise<ServiceCollectionResponse>}
     */
     get(requestConfiguration?: RequestConfiguration<ServicesRequestBuilderGetQueryParameters> | undefined) : Promise<ServiceCollectionResponse | undefined>;
    /**
     * Gets a collection of all catalog service entries. This operations endpoint allows viewing all services regardless of their availability to specific accounts.
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {RequestInformation}
     */
     toGetRequestInformation(requestConfiguration?: RequestConfiguration<ServicesRequestBuilderGetQueryParameters> | undefined) : RequestInformation;
}
/**
 * Gets a collection of all catalog service entries. This operations endpoint allows viewing all services regardless of their availability to specific accounts.
 */
export interface ServicesRequestBuilderGetQueryParameters {
    /**
     * The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.
     */
    accountId?: string;
    /**
     * The category identifier to filter services by. Available options include: `Data` (services focused on data storage, processing, and management), `Ingress` (services for data acquisition and input handling), and `null` (returns services from all categories). When provided, only returns services that belong to the specified category.
     */
    category?: CategoryNullable;
}
/**
 * Uri template for the request builder.
 */
export const ServicesRequestBuilderUriTemplate = "{+baseurl}/ops/catalog/v2/services{?accountId*,category*}";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const ServicesRequestBuilderNavigationMetadata: Record<Exclude<keyof ServicesRequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    byId: {
        requestsMetadata: ServicesItemRequestBuilderRequestsMetadata,
        pathParametersMappings: ["id"],
    },
};
/**
 * Metadata for all the requests in the request builder.
 */
export const ServicesRequestBuilderRequestsMetadata: RequestsMetadata = {
    get: {
        uriTemplate: ServicesRequestBuilderUriTemplate,
        responseBodyContentType: "application/json",
        adapterMethodName: "send",
        responseBodyFactory:  createServiceCollectionResponseFromDiscriminatorValue,
    },
};
/* tslint:enable */
/* eslint-enable */
