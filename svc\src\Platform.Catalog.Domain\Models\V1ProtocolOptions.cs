﻿namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// Base class for protocol options for service entries.
    /// </summary>
    public class V1ProtocolOptions
    {
        /// <summary>
        /// Gets or sets the SolutionDefinition.
        /// </summary>
        public string? SolutionDefinition { get; set; }

        /// <summary>
        /// Gets or sets the Mappings for the legacy protocol.
        /// </summary>
        public V1LegacyProtocolMappings? Mappings { get; set; }

        /// <summary>
        /// Gets or sets the webhook URI.
        /// </summary>
        public Uri? WebhookUri { get; set; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is V1ProtocolOptions item
                && string.Equals(item.SolutionDefinition, SolutionDefinition, StringComparison.InvariantCultureIgnoreCase)
                && ((item.Mappings == null && Mappings == null) || (item.Mappings != null && item.Mappings.Equals(Mappings)))
                && ((item.WebhookUri == null && WebhookUri == null) || (item.WebhookUri != null && item.WebhookUri.Equals(WebhookUri)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return HashCode.Combine(
                SolutionDefinition?.ToUpperInvariant(),
                Mappings,
                WebhookUri);
        }
    }
}