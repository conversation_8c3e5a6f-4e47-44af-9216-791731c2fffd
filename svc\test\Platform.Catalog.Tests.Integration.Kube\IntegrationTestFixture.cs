﻿using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.Json;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Tests.Integration.Kube;
using Aveva.Platform.Catalog.Tests.Integration.Kube.Common;
using k8s;
using k8s.Autorest;
using k8s.Models;
using Microsoft.Extensions.Logging;
using Platform.Catalog.Tests.Integration.Kube.Common;
using Polly;

namespace Platform.Catalog.Tests.Integration.Kube;

[SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
[SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
public sealed class IntegrationTestFixture : IDisposable
{
    private const string ImagePullSecret = "acr-secret";

    private readonly JsonSerializerOptions _prettyPrintOptions = new() { WriteIndented = true };
    private readonly IntegrationTestOptions _options;
    private readonly Kubernetes _kubeClient;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger<IntegrationTestFixture> _logger;
    private readonly HelmClient _helmClient;
    private readonly HelmfileClient _helmfileClient;
    private readonly KubectlClient _kubectlClient;
    private readonly Kubernetes _k8s;
    private readonly GenericClient _genericClient;
    private bool _disposed;

    public IntegrationTestFixture()
    {
        _options = new IntegrationTestOptions();
        _kubeClient = new Kubernetes(KubernetesClientConfiguration.BuildConfigFromConfigFile(currentContext: _options.KubeContext));
        _loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.SetMinimumLevel(LogLevel.Trace);
            builder.AddConsole();
        });
        _logger = _loggerFactory.CreateLogger<IntegrationTestFixture>();
        _helmClient = new HelmClient(_options, _logger, _kubeClient);
        _helmfileClient = new HelmfileClient(_options, _logger, _kubeClient);
        _kubectlClient = new KubectlClient(_options, _logger);
        TestClient = new HttpClient()
        {
            BaseAddress = new Uri($"http://localhost:{_options.CatalogLocalPort}"),
        };
        _logger.LogInformation($"Integration Test Run With Options \n {JsonSerializer.Serialize(_options, _prettyPrintOptions)}");
        var k8sConfig = KubernetesClientConfiguration.BuildDefaultConfig();
        _k8s = new Kubernetes(k8sConfig);
        _genericClient = new GenericClient(_k8s, CatalogConstants.Group, CatalogConstants.V1, CatalogConstants.ServiceEntry.Plural);
        SetupAsync().Wait();

        _logger.LogInformation($"Setup finished.");
    }

    public HttpClient TestClient { get; }
    internal HelmClient HelmClient { get => _helmClient; }
    internal HelmfileClient HelmfileClient { get => _helmfileClient; }
    internal Kubernetes KubeClient { get => _kubeClient; }
    internal KubectlClient KubectlClient { get => _kubectlClient; }
    internal IntegrationTestOptions Options { get => _options; }
    internal GenericClient GenericClient { get => _genericClient; }

    internal RabbitmqClient? RabbitmqClient { get; private set; }

    public void Dispose()
    {
        Task.Run(async () =>
        {
            _logger.LogDebug(await _kubectlClient.ExecuteAsync($"delete ns {_options.TestDeploymentNamespace}").ConfigureAwait(false));
        }).Wait();

        if (!_disposed)
        {
            _disposed = true;
            _kubeClient?.Dispose();
            _loggerFactory?.Dispose();
            _k8s?.Dispose();
            _genericClient?.Dispose();
        }
    }

    private async Task SetupAsync()
    {
        await CheckHelmCliInstalledAsync().ConfigureAwait(false);
        await CheckHelmfileCliInstalledAsync().ConfigureAwait(false);
        await CheckKubectlCliInstalledAsync().ConfigureAwait(false);
        var maxTries = 3;

        var tries = 0;
        bool podsReady;
        var podsNotReadyNamespaces = new List<string>();
        do
        {
            await PublishHelmfileAsync().ConfigureAwait(false);
            podsReady = true;

            if (!await WaitForPods(_options.DaprNamespace, maxTries: 20, wait: 6).ConfigureAwait(false))
            {
                podsReady = false;
                podsNotReadyNamespaces.Add(_options.DaprNamespace);
            }

            if (!await WaitForPods(_options.CatalogReleaseNamespace, maxTries: 40, wait: 6).ConfigureAwait(false))
            {
                podsReady = false;
                podsNotReadyNamespaces.Add(_options.CatalogReleaseNamespace);
            }
        }
        while (++tries < maxTries && !podsReady);

        if (!podsReady)
        {
            throw new InvalidOperationException($"Pods not ready in namespaces: {string.Join(", ", podsNotReadyNamespaces.Distinct())}");
        }

        await PortForwardPodAsync(Options.RabbitmqNamespace, Options.RabbitmqPodName, Options.RabbitmqAmqPort, Options.RabbitmqAmqPort).ConfigureAwait(false);
        RabbitmqClient = new RabbitmqClient(Options.RabbitmqHostName, Options.RabbitmqAmqPort, Options.RabbitmqAdminUser, Options.RabbitmqAdminSecret, _logger);
        _ = Task.Run(() =>
        {
            RabbitmqClient.RegisterEventListenerAsync(Options.CatalogEvents);
        });

        // Introducing this delay. So that RabbitMq event consumer will be listening to the events generated by DeployTestDataAsync.
        await Task.Delay(TimeSpan.FromSeconds(5)).ConfigureAwait(true);

        await PortForwardPodAsync(Options.CatalogReleaseNamespace, "catalog", 8080, Options.CatalogLocalPort).ConfigureAwait(false);
        await DeployTestDataAsync().ConfigureAwait(false);
    }

    private async Task DeployTestDataAsync()
    {
        var currentDirectory = Environment.CurrentDirectory;
        var chartPath = Path.GetFullPath(Path.Combine(currentDirectory, "./TestAssets/chart"));

        var policy = Policy.Handle<Exception>(ex =>
        {
            _logger.LogWarning($"Attempt failed: {ex.Message}");
            return true;
        })
        .WaitAndRetryAsync(3, retryAttempt =>
        {
            var delay = TimeSpan.FromSeconds(5);
            return delay;
        });

        await policy.ExecuteAsync(async () => await _helmClient.InstallReleaseAsync(
                    chartPath,
                    _options.TestDeploymentReleaseName,
                    _options.TestDeploymentNamespace,
                    $"--set catalogNamespace={_options.CatalogReleaseNamespace} --timeout 2m0s").ConfigureAwait(false)).ConfigureAwait(false);
    }

    private async Task CheckHelmCliInstalledAsync()
    {
        if (!await _helmClient.IsCliInstalledAsync().ConfigureAwait(false))
        {
            throw new InvalidOperationException($"Helm CLI Could not be found or there is a problem at {_options.HelmExecutable}");
        }
    }

    private async Task CheckHelmfileCliInstalledAsync()
    {
        if (!await _helmfileClient.IsCliInstalledAsync().ConfigureAwait(false))
        {
            throw new InvalidOperationException($"Helmfile CLI Could not be found or there is a problem at {_options.HelmfileExecutable}");
        }
    }

    private async Task CheckKubectlCliInstalledAsync()
    {
        if (!await _kubectlClient.IsCliInstalledAsync().ConfigureAwait(false))
        {
            throw new InvalidOperationException($"Kubectl CLI Could not be found or there is a problem at {_options.KubectlExecutable}");
        }
    }

    private async Task CreateNamespaceAsync()
    {
        try
        {
            _logger.LogInformation($"Creating namespace...");
            await _kubeClient.CreateNamespaceAsync(new V1Namespace
            {
                Metadata = new V1ObjectMeta
                {
                    Name = _options.CatalogReleaseNamespace,
                },
            }).ConfigureAwait(false);
        }
        catch (HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.Conflict)
        {
        }
    }

    private async Task CreateSecretAsync()
    {
        try
        {
            _logger.LogInformation($"Creating docker secret...");
            await _kubeClient.CreateNamespacedSecretAsync(
                new V1Secret
                {
                    Metadata = new V1ObjectMeta
                    {
                        Name = ImagePullSecret,
                        NamespaceProperty = _options.CatalogReleaseNamespace,
                    },
                    Type = "kubernetes.io/dockerconfigjson",
                    Data = new Dictionary<string, byte[]>
                    {
                        {
                            ".dockerconfigjson",
                            Encoding.UTF8.GetBytes(JsonSerializer.Serialize(new
                            {
                                auths = new Dictionary<string, object>
                                {
                                    {
                                        _options.ImageRegistry!,
                                        new
                                        {
                                            username = _options.DockerUsername,
                                            password = _options.DockerPassword,
                                        }
                                    },
                                },
                            }))
                        },
                    },
                },
                _options.CatalogReleaseNamespace).ConfigureAwait(false);
        }
        catch (HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.Conflict)
        {
        }
    }

    private async Task DeployDaprHelmChart()
    {
        _logger.LogInformation($"Adding {_options.DaprRepoName} repository...");
        await _helmClient.AddRepoAsync(_options.DaprReleaseName, _options.DaprRepoUrl).ConfigureAwait(false);

        _logger.LogInformation($"Installing {_options.DaprReleaseName} helm chart...");
        await _helmClient.InstallReleaseAsync($"{_options.DaprRepoName}/{_options.DaprReleaseName}", _options.DaprReleaseName, _options.DaprNamespace).ConfigureAwait(false);
    }

    private async Task<PortForwarder> PortForwardPodAsync(string @namespace, string name, int sourcePort, int targetPort)
    {
        var pods = await Helpers.GetPods(_kubeClient, @namespace).ConfigureAwait(false);
        var pod = pods.FirstOrDefault(x => x.Metadata.Name.StartsWith(name, StringComparison.OrdinalIgnoreCase)) ?? throw new InvalidOperationException($"Pod where name contains {name} not found");
        var portForwader = new PortForwarder(_kubeClient, pod, sourcePort, targetPort);
        await portForwader.Forward().ConfigureAwait(false);
        return portForwader;
    }

    private async Task PublishHelmfileAsync()
    {
        var currentDirectory = Environment.CurrentDirectory;
        var helmfilePath = Path.GetFullPath(Path.Combine(currentDirectory, _options.CatalogHelmfilePath));

        if (RequireImagePullSecret())
        {
            _logger.LogInformation($"Docker pull secret required.");

            await CreateNamespaceAsync().ConfigureAwait(false);
            await CreateSecretAsync().ConfigureAwait(false);
        }

        var imageRegistry = string.IsNullOrWhiteSpace(_options.ImageRegistry) ? string.Empty : $"{_options.ImageRegistry}";
        var arguments = new List<string>
        {
            $"--set environment.containerRegistryServer={imageRegistry}",
            $"--set buildVariables.tag={_options.ImageTag}",
        };
        if (RequireImagePullSecret())
        {
            arguments.AddRange(
            [
                $"--set image.pullSecret={ImagePullSecret}",
            ]);
        }

        var installArguments = string.Join(" ", arguments);

        var response = await _helmfileClient.ListAsync(helmfilePath).ConfigureAwait(false);
        _logger.LogInformation($"Deploying helmfile release:");
        _logger.LogInformation(response);

        await _helmfileClient.SyncAsync(helmfilePath, installArguments).ConfigureAwait(false);
    }

    private bool RequireImagePullSecret()
    {
        return !string.IsNullOrWhiteSpace(_options.DockerUsername) && !string.IsNullOrWhiteSpace(_options.DockerPassword) && !string.IsNullOrWhiteSpace(_options.ImageRegistry);
    }

    private async Task<bool> WaitForPods(string @namespace, int maxTries, int wait)
    {
        _logger.LogInformation($"Wait for pods in namespace {@namespace} to be ready...");
        var ready = await Helpers.WaitForPods(_kubeClient, @namespace, maxTries, wait).ConfigureAwait(false);
        if (ready)
        {
            _logger.LogInformation($"Pods in namespace {@namespace} ready.");
        }
        else
        {
            _logger.LogInformation($"Pods in namespace {@namespace} not ready.");
        }

        var pods = await Helpers.GetPods(_kubeClient, @namespace).ConfigureAwait(true);
        _logger.LogInformation(Helpers.GetPodTable(pods));

        if (!ready)
        {
            var events = await _kubeClient.EventsV1.ListNamespacedEventAsync(@namespace).ConfigureAwait(false);
            _logger.LogWarning(string.Join("\n", events.Items.OrderByDescending(x => x.EventTime).Select(x => $"Reason: {x.Reason}\n Object: {x.Regarding}\n Message: {x.Note}")));
        }

        return ready;
    }
}