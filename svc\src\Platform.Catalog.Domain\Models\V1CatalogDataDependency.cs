﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// CatalogItemDependency.
/// </summary>
public class V1CatalogDataDependency
{
#pragma warning disable CS8618
    /// <summary>
    /// Gets or sets type.
    /// </summary>
    [Required]
    public V1CatalogDataDependencyType Type { get; set; }

    /// <summary>
    /// Gets or sets cardinality.
    /// </summary>
    [Required]
    public V1CatalogDataDependencyCardinality Cardinality { get; set; }

    /// <summary>
    /// Gets or sets colocated.
    /// </summary>
    public bool? Colocated { get; set; }

    /// <summary>
    /// Gets or sets config.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public IDictionary<string, V1CatalogDataDependencyConfig>? Config { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        return obj is V1CatalogDataDependency item && string.Equals(item.Type.ToString(), Type.ToString(), StringComparison.InvariantCultureIgnoreCase)
                 && string.Equals(item.Cardinality.ToString(), Cardinality.ToString(), StringComparison.InvariantCultureIgnoreCase)
                 && item.Colocated == Colocated
                 && ConfigsAreEqual(item.Config);
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
#pragma warning restore CS8618

    private bool ConfigsAreEqual(IDictionary<string, V1CatalogDataDependencyConfig>? config)
    {
        if ((config == null || config.Count == 0) && (Config == null || Config.Count == 0))
        {
            return true;
        }

        if (config == null || Config == null ||
            config.Count != Config.Count)
        {
            return false;
        }

        foreach (var kvp in config)
        {
            if (!Config.TryGetValue(kvp.Key, out var dependencyConfig) || !kvp.Value.Equals(dependencyConfig))
            {
                return false;
            }
        }

        return true;
    }
}