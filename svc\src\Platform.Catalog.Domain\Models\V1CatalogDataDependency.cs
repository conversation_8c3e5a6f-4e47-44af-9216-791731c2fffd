namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// Defines a relationship between catalog services where one service requires functionality from another service.
/// </summary>
public class V1CatalogDataDependency
{
#pragma warning disable CS8618
    /// <summary>
    /// Specifies whether the dependent service must be available when the catalog service is created.
    /// </summary>
    public V1CatalogDataDependencyType? Type { get; set; }

    /// <summary>
    /// Specifies whether the catalog service can connect to one or many instances of the dependent service.
    /// </summary>
    public V1CatalogDataDependencyCardinality? Cardinality { get; set; }

    /// <summary>
    /// Indicates whether the dependent service must be located in the same geography.
    /// </summary>
    public bool? Colocated { get; set; }

    /// <summary>
    /// The identifier of a related dependency.
    /// </summary>
    public string? Related { get; set; }

    /// <summary>
    /// Additional configuration values for integrating dependent services.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public IDictionary<string, V1CatalogDataDependencyConfig>? Config { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        return obj is V1CatalogDataDependency item
                 && ((item.Type == null && Type == null) || (item.Type != null && Type != null && string.Equals(item.Type.ToString(), Type.ToString(), StringComparison.InvariantCultureIgnoreCase)))
                 && ((item.Cardinality == null && Cardinality == null) || (item.Cardinality != null && Cardinality != null && string.Equals(item.Cardinality.ToString(), Cardinality.ToString(), StringComparison.InvariantCultureIgnoreCase)))
                 && ((item.Related == null && Related == null) || (item.Related != null && Related != null && string.Equals(item.Related, Related, StringComparison.InvariantCultureIgnoreCase)))
                 && item.Colocated == Colocated
                 && ConfigsAreEqual(item.Config);
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
#pragma warning restore CS8618

    private bool ConfigsAreEqual(IDictionary<string, V1CatalogDataDependencyConfig>? config)
    {
        if ((config == null || config.Count == 0) && (Config == null || Config.Count == 0))
        {
            return true;
        }

        if (config == null || Config == null ||
            config.Count != Config.Count)
        {
            return false;
        }

        foreach (var kvp in config)
        {
            if (!Config.TryGetValue(kvp.Key, out var dependencyConfig) || !kvp.Value.Equals(dependencyConfig))
            {
                return false;
            }
        }

        return true;
    }
}