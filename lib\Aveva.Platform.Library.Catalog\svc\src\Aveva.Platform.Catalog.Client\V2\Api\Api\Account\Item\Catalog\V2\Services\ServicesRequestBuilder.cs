// <auto-generated/>
#pragma warning disable CS0618
using Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.Item;
using Aveva.Platform.Catalog.Client.V2.Api.Models;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services
{
    /// <summary>
    /// Builds and executes requests for operations under \api\account\{accountId}\catalog\v2\services
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ServicesRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the Aveva.Platform.Catalog.Client.V2.Api.api.account.item.catalog.v2.services.item collection</summary>
        /// <param name="position">The unique identifier for this service. This ID is used to reference the service in other operations.</param>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.Item.ServicesItemRequestBuilder"/></returns>
        public global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.Item.ServicesItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("id", position);
                return new global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.Item.ServicesItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ServicesRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account/{accountId}/catalog/v2/services{?category*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ServicesRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account/{accountId}/catalog/v2/services{?category*}", rawUrl)
        {
        }
        /// <summary>
        /// Gets a collection of catalog service entries for an account. This endpoint returns only services that are made available to your account.
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Models.ServiceCollectionResponse"/></returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::Aveva.Platform.Catalog.Client.V2.Api.Models.ServiceCollectionResponse?> GetAsync(Action<RequestConfiguration<global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder.ServicesRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::Aveva.Platform.Catalog.Client.V2.Api.Models.ServiceCollectionResponse> GetAsync(Action<RequestConfiguration<global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder.ServicesRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            return await RequestAdapter.SendAsync<global::Aveva.Platform.Catalog.Client.V2.Api.Models.ServiceCollectionResponse>(requestInfo, global::Aveva.Platform.Catalog.Client.V2.Api.Models.ServiceCollectionResponse.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <summary>
        /// Gets a collection of catalog service entries for an account. This endpoint returns only services that are made available to your account.
        /// </summary>
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder.ServicesRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder.ServicesRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder WithUrl(string rawUrl)
        {
            return new global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder(rawUrl, RequestAdapter);
        }
        /// <summary>
        /// Gets a collection of catalog service entries for an account. This endpoint returns only services that are made available to your account.
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class ServicesRequestBuilderGetQueryParameters 
        {
            /// <summary>The category identifier to filter services by. Available options include: `Data` (services focused on data storage, processing, and management), `Ingress` (services for data acquisition and input handling), and `null` (returns services from all categories). When provided, only returns services that belong to the specified category.</summary>
            [QueryParameter("category")]
            public global::Aveva.Platform.Catalog.Client.V2.Api.Models.CategoryNullable? Category { get; set; }
        }
    }
}
#pragma warning restore CS0618
