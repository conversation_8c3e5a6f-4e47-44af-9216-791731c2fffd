﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Aveva.Platform.Catalog.Api.Swagger.SchemaFilters;

/// <summary>
/// A schema filter for configuring the OpenAPI schema of ProtocolOptions.
/// This filter handles the polymorphic nature of ProtocolOptions.
/// </summary>
public class ProtocolOptionsSchemaFilter : ISchemaFilter
{
    /// <summary>
    /// Applies the schema filter to modify the OpenAPI schema for ProtocolOptions.
    /// </summary>
    /// <param name="schema">The OpenAPI schema to modify.</param>
    /// <param name="context">The schema filter context containing type and other metadata.</param>
    /// <exception cref="ArgumentNullException">Thrown when <paramref name="schema"/> or <paramref name="context"/> is null.</exception>
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        ArgumentNullException.ThrowIfNull(schema);
        ArgumentNullException.ThrowIfNull(context);

        if (context.Type != typeof(ProtocolOptions))
        {
            return;
        }

        // Clear any auto-generated properties and type information to create a pure oneOf schema
        schema.Properties?.Clear();
        schema.Type = null;
        schema.AdditionalProperties = null;

        // Add the possible types
        schema.OneOf = new List<OpenApiSchema>
        {
            context.SchemaGenerator.GenerateSchema(typeof(LegacyProtocolOptions), context.SchemaRepository),
            context.SchemaGenerator.GenerateSchema(typeof(WebhookProtocolOptions), context.SchemaRepository),
        };

        // Add description to explain the relationship
        schema.Description = "The protocol options. The actual type is determined by the Protocol property. " +
                           "When Protocol is `Legacy`, this will be a LegacyProtocolOptions. " +
                           "When Protocol is `Webhook`, this will be a WebhookProtocolOptions.";
    }
}