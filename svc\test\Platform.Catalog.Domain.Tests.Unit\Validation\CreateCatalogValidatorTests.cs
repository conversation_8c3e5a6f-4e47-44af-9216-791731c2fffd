﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Validation;
using FluentValidation;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Validation;

/// <summary>
/// <see cref="CreateCatalogValidator"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public class CreateCatalogValidatorTests
{
    #region Test Cases

    [Fact]
    public async Task CreateCatalogValidator_ValidateCatalogIdNotReused_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Availability = new V1ServiceAvailability()
            {
                Limit = 5,
                Enabled = true,
            },
            ExternalIdentities =
            [
                new V1ExternalIdentity()
                {
                    Id = "guid1",
                    Scopes = [V1Scope.apiRole],
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                },
            ],
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateCatalogIdNotReused_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(2);
        result.Errors.Exists(e => e.ErrorMessage.Equals("Service entry Id 1 is already taken by another service.", StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.ErrorMessage.Equals("The first 16 characters of the service entry Id 1 is not unique.It matches with other service entry Id.", StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_IdTooLong_Throws()
    {
        // Arrange
        var id = "a";
        for (int i = 0; i < 200; i++)
        {
            id += "a";
        }

        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = "name",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe("The length of 'Id' must be 38 characters or fewer. You entered 201 characters.");
    }

    [Fact]
    public async Task CreateCatalogValidator_NameTooLong_Throws()
    {
        // Arrange
        var name = "a";
        for (int i = 0; i < 200; i++)
        {
            name += "a";
        }

        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = name,
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe("The length of 'Display Name' must be 100 characters or fewer. You entered 201 characters.");
    }

    [Fact]
    public async Task CreateCatalogValidator_DescriptionTooLong_Throws()
    {
        // Arrange
        var description = new string('*', 2001);

        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "name",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = description,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe("Description is required. Length should be < 2000.");
    }

    [Fact]
    public async Task CreateCatalogValidator_ExternalIdentityWithEmptyScope_Throws()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "name",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            ExternalIdentities =
            [
                new V1ExternalIdentity()
                {
                    Id = "guid1",
                    Scopes = [],
                },
            ],
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe($"{nameof(V1ExternalIdentity.Scopes)} cannot be empty for 'guid1' identity.");
    }

    [Fact]
    public async Task CreateCatalogValidator_ExternalIdentityWithDuplicateScope_Throws()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "name",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            ExternalIdentities =
            [
                new V1ExternalIdentity()
                {
                    Id = "guid1",
                    Scopes = [V1Scope.apiRole, V1Scope.apiRole],
                },
            ],
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe($"{nameof(V1ExternalIdentity.Scopes)} cannot contain duplicate scopes for 'guid1' identity.");
    }

    [Fact]
    public async Task CreateCatalogValidator_LifecycleMissing_Throws()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBeGreaterThan(0);
        result.Errors[0].ErrorMessage.ShouldBe("Lifecycle is required");
    }

    [Fact]
    public async Task CreateCatalogValidator_AvailabilityOnNonCatalogTriggered_Throws()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Availability = new V1ServiceAvailability()
            {
                Limit = 5,
                Enabled = true,
            },
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBeGreaterThan(0);
        result.Errors[0].ErrorMessage.ShouldBe("ServiceEntry Availability cannot be set if the lifecycle trigger is not catalog.");
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateInstanceMode_Throws()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle()
            {
                Trigger = V1Trigger.Account,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "provider",
            },
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].ErrorMessage.ShouldBe("InstanceMode is required for Account trigger.");
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateDependenciesExist_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>()
            {
                {
                    "dependency", new V1CatalogDataDependency()
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                    }
                },
            },
        };
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateDependenciesExist_Throws()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency2", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };

        var subject = new CreateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Dependant Service entry dependency2 is not found.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateDependenciesExist_Skip_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency2", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };

        var subject = new CreateCatalogValidator(oldEntries, true);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateDependencies_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Colocated = true,
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };

        var subject = new CreateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateDependencies_Config_Succeeds()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Colocated = true,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 10, Max = 50 } },
                        },
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };

        var subject = new CreateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateDependencies_Config_Throws()
    {
        // Arrange
        var oldEntry = new ServiceResponse
        {
            Id = "dependency",
            DisplayName = "dependency",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        var newEntry = new V1ServiceEntry
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dependency", new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Colocated = true,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            { "config1", new V1CatalogDataDependencyConfig { Label = string.Empty, Help = "help1", Min = -1, Max = 10 } },
                            { "config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = string.Empty, Min = 50, Max = 10 } },
                        },
                    }
                },
            },
        };
        var validationContext = new ValidationContext<V1ServiceEntry>(newEntry);
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        var subject = new CreateCatalogValidator(oldEntries, false);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(4);
        result.Errors.Exists(e => e.PropertyName.Equals("Label") && e.ErrorMessage.Equals(
            "Required for the dependency: dependency config: config1",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.PropertyName.Equals("Min") && e.ErrorMessage.Equals(
            "Must be positive for the dependency: dependency config: config1",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.PropertyName.Equals("Help") && e.ErrorMessage.Equals(
            "Required for the dependency: dependency config: config2",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.PropertyName.Equals("Max") && e.ErrorMessage.Equals(
            "Must be greater than or equal to min for the dependency: dependency config: config2",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateServiceIdUniqueInFirst16Characters_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "*********",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        CreateCatalogValidator subject = new CreateCatalogValidator(null, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateServiceIdUniqueInFirst16Characters_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "serviceid1234567",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "serviceid1234567890",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "The first 16 characters of the service entry Id serviceid1234567890 is not unique.It matches with other service entry Id.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateGeographies_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.None, null, string.Empty, null, null, false),
            Geographies = [new() { Id = "eu" }],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateGeographiesEmpty_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.None, null, string.Empty, null, null, false),
            Geographies = [],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Geographies must have at least one geography for HostingType 'External'.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateGeographiesNotExternal_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.None, null, string.Empty, null, null, false),
            Geographies = [new() { Id = "eu" }],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Geographies only valid for HostingType 'External'.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateApplication_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications =
            [
                new()
                {
                    Name = "testName",
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateApplicationDefaultUrl_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications =
            [
                new()
                {
                    Name = "testName",
                    Urls = new Dictionary<string, string>()
                    {
                        { "default", "url1" },
                    },
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateApplicationUrls_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications =
            [
                new()
                {
                    Name = "testName",
                    Urls = new Dictionary<string, string>()
                    {
                        { "default", "url1" },
                        { "eu", "url" },
                    },
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateApplicationNoDefaultUrl_Throws()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "application",
            DisplayName = "application",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Applications =
            [
                new()
                {
                    Name = "testName",
                    Urls = new Dictionary<string, string>()
                    {
                        { "eu", "url" },
                    },
                },
            ],
        };
        ServiceCollectionResponse oldEntries = new ServiceCollectionResponse() { Items = [oldEntry] };
        CreateCatalogValidator subject = new CreateCatalogValidator(oldEntries, false);
        ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(newEntry);

        // Act
        var result = await subject.ValidateAsync(validationContext, cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Application testName urls must also contain a default entry.",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenProtocolIncorrect_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://example.com/webhook"),
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(2);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "SolutionDefinition required for the Legacy Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        result.Errors.Exists(e => e.ErrorMessage.Equals(
           "Mappings required for the Legacy Protocol",
           StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenProtocolOptionsNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "ProtocolOptions required for the Legacy Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenSolutionDefinitionNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = null,
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "SolutionDefinition required for the Legacy Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenMappingsNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = null,
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Mappings required for the Legacy Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenMappingsGeographiesNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = null,
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "You must have geographies and at least a 'default' geography mapping",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenMappingsGeographiesDoesntContainDefaultKey_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = [],
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "You must have at least a 'default' geography mapping",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependenciesNull_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = null,
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependenciesEmpty_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>(),
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependencyMissingIntegrationDefinition_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = null!,
                                }
                            },
                        },
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Integration definition is required for dependency 'dep1'",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependencySourceContextNull_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = null,
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependencySourceContextEmpty_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = string.Empty,
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Source context is required for dependency 'dep1'",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependencySourceContextWhitespace_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = "   ",
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Source context is required for dependency 'dep1'",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenSourceContextConfigNotRequired_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "context",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = false,
                                    Max = 255,
                                    Label = "Context",
                                    Help = "The context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = "context",
                                    TargetContextConfig = null,
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Source context config 'context' in dependency 'dep1' must be marked as required",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenSourceContextConfigDoesNotExist_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "otherContext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Other Context",
                                    Help = "The other context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = "context",
                                    TargetContextConfig = null,
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Source context config 'context' does not exist in dependency 'dep1' config",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenSourceContextConfigRequired_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "context",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Context",
                                    Help = "The context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = "context",
                                    TargetContextConfig = "context",
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependencyMissingTargetContext_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    TargetContextConfig = "   ",
                                }
                            },
                        },
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "Target context is required for dependency 'dep1'",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateLegacyProtocol_WhenDependencyComplete_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "context",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Context",
                                    Help = "The context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                        {
                            {
                                "dep1", new V1LegacyProtocolDependencyMapping
                                {
                                    IntegrationDefinition = "integration",
                                    SourceContextConfig = "context",
                                    TargetContextConfig = "context",
                                }
                            },
                        },
                    },
                },
                false),
        };

        var existingEntries = new ServiceCollectionResponse
        {
            Items = new List<ServiceResponse>
            {
                new ServiceResponse
                {
                    Id = "dep1",
                    DisplayName = "Dependency 1",
                    HostingType = "Environment",
                    Lifecycle = new Lifecycle
                    {
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "provider",
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                    },
                },
            },
        };
        var subject = new CreateCatalogValidator(existingEntries, false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateWebhookProtocol_WhenProtocolIncorrect_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Geographies = new Dictionary<string, string>
                        {
                            { "default", "region1" },
                        },
                    },
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "WebhookUri required for the Webhook Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateWebhookProtocol_WhenProtocolOptionsNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "ProtocolOptions required for the Webhook Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateWebhookProtocol_WhenWebhookUriNull_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = null!,
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "WebhookUri required for the Webhook Protocol",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateWebhookProtocol_WhenWebhookUriRelative_ReturnsValidationFailure()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("relative/path", UriKind.Relative),
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(1);
        result.Errors.Exists(e => e.ErrorMessage.Equals(
            "WebhookUri must be a valid absolute URI",
            StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
    }

    [Fact]
    public async Task CreateCatalogValidator_ValidateWebhookProtocol_WhenWebhookUriValid_Succeeds()
    {
        // Arrange
        var serviceEntry = new V1ServiceEntry()
        {
            Id = "newEntry",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://example.com/webhook"),
                },
                false),
        };
        var subject = new CreateCatalogValidator(new ServiceCollectionResponse(), false);

        // Act
        var result = await subject.ValidateAsync(new ValidationContext<V1ServiceEntry>(serviceEntry), cancellation: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        result.Errors.Count.ShouldBe(0);
    }

    #endregion Test Cases
}