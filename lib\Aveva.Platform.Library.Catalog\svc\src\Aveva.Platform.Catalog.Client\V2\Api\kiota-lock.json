{"descriptionHash": "9CEB9194EC729F27F0B3A4011D2B67288EAC53CE426EFCA186309B30B431090B1A7C6697388C1CF15666F2F6D3D1A97D18031DDBB7BC0E10977AFAC5CFAE8135", "descriptionLocation": "../../../../../../../svc/src/Platform.Catalog/bin/Debug/net8.0/platform-catalog-public-api-v2-swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.24.3", "clientClassName": "Client", "typeAccessModifier": "Public", "clientNamespaceName": "Aveva.Platform.Catalog.Client.V2.Api", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": true, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}