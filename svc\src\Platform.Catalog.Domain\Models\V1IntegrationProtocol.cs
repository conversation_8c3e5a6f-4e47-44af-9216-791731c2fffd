namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// Specifies the communication protocol used for service lifecycle management and integration events.
    /// </summary>
    public enum V1IntegrationProtocol
    {
        /// <summary>
        /// Uses the default integration event protocol with publish-subscribe messaging patterns.
        /// </summary>
        IntegrationEvent,

        /// <summary>
        /// Uses the legacy Solution and Capability Management (SCM) protocol for backward compatibility.
        /// </summary>
        Legacy,

        /// <summary>
        /// Uses webhook-based HTTP callbacks for real-time event notifications.
        /// </summary>
        Webhook,
    }
}