﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Events.Converter;
using Aveva.Platform.Catalog.Events.Logging;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using Polly;

namespace Aveva.Platform.Catalog.Events.Publisher
{
    /// <summary>
    /// Catalog ServiceEntry update event publisher class.
    /// </summary>
    public sealed class CatalogServiceUpdateV1Publisher : IEventPublisher
    {
        private static string _geography = string.Empty;
        private static string _region = string.Empty;
        private readonly IEventBus _eventBus;
        private readonly ILogger? _logger;
        private readonly ICatalogClient _catalogClient;
        private readonly ResiliencePipeline _retryPipeline;
        private readonly ITypeMappingService _typeMappingService;
        private readonly EventMetrics _metrics;

        /// <summary>
        /// Initializes a new instance of the <see cref="CatalogServiceUpdateV1Publisher"/> class.
        /// </summary>
        /// <param name="eventBus">Event bus to publish event.</param>
        /// <param name="catalogClient">Catalog Api client.</param>
        /// <param name="geography">Geography where Catalog Event Service is running.</param>
        /// <param name="region">Region where Catalog Event Service is running.</param>
        /// <param name="retryPipeline">Polly ResiliencePipeline instance.</param>
        /// <param name="logger">Instance of ILogger.</param>
        /// <param name="typeMapper">Instance of ITypeMappingService for domain model mapping.</param>
        /// <param name="metrics">CatalogMetrics class for instrumentation.</param>
        public CatalogServiceUpdateV1Publisher(
            IEventBus eventBus,
            ICatalogClient catalogClient,
            string geography,
            string region,
            ResiliencePipeline retryPipeline,
            ITypeMappingService typeMapper,
            EventMetrics metrics,
            ILogger? logger = null)
        {
            ArgumentNullException.ThrowIfNull(eventBus, nameof(eventBus));
            ArgumentException.ThrowIfNullOrEmpty(geography, nameof(geography));
            ArgumentException.ThrowIfNullOrEmpty(region, nameof(region));
            ArgumentNullException.ThrowIfNull(catalogClient, nameof(catalogClient));
            ArgumentNullException.ThrowIfNull(retryPipeline, nameof(retryPipeline));
            ArgumentNullException.ThrowIfNull(typeMapper, nameof(typeMapper));
            ArgumentNullException.ThrowIfNull(metrics, nameof(typeMapper));
            ArgumentNullException.ThrowIfNull(metrics, nameof(EventMetrics));

            _eventBus = eventBus;
            _geography = geography;
            _region = region;
            _logger = logger;
            _catalogClient = catalogClient;
            _retryPipeline = retryPipeline;
            _typeMappingService = typeMapper;
            _metrics = metrics;
        }

        /// <summary>
        /// Publishes an update event for a service entry.
        /// </summary>
        /// <param name="newServiceEntry">The new service entry.</param>
        /// <param name="oldServiceEntry">The old service entry.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [SuppressMessage("Naming", "CA1031:Do not catch general exception types", Justification = "We will be taking the same action for all exceptions. So specific exception catching is not required.", Scope = "module")]
        public async Task PublishEventAsync(V1ServiceEntry newServiceEntry, V1ServiceEntry? oldServiceEntry)
        {
            ArgumentNullException.ThrowIfNull(oldServiceEntry, nameof(oldServiceEntry));
            ArgumentNullException.ThrowIfNull(newServiceEntry, nameof(newServiceEntry));

            var serviceId = newServiceEntry.Id;
            var updateEvent = new CatalogServiceUpdateV1()
            {
                Id = serviceId,
                Geography = _geography,
                Region = _region,
                NewServiceEntry = newServiceEntry.ToDto(),
                OldServiceEntry = oldServiceEntry.ToDto(),
            };

            using var activity = CatalogTraceSource.EventsTrace.StartActivity("catalog.serviceEntry.updateevent", ActivityKind.Producer);
            {
                _logger?.CatalogServicEntryUpdateEvent(serviceId, _region);
                var timer = new Stopwatch();
                timer.Start();
                try
                {
                    // We wanted to update all the dependencies with null "Colocated" property to false.
                    // Reason: This is due to the difference in nullability of "Colocated" property between GetCatalogResponseInternal
                    // and V1ServiceEntry model. This causes issues while comparing V1ServiceEntry model (from webhook) and the mapped
                    // V1ServiceEntry model (from catalog api GetCatalogResponseInternal response)
                    newServiceEntry.Dependencies?.Where(x => x.Value.Colocated == null)?.ToList().ForEach(dependencies =>
                    {
                        dependencies.Value.Colocated = false;
                    });
                    var mappedServiceResponse = MapServiceAvailability(_typeMappingService.Map<V1ServiceEntry, ServiceResponse>(newServiceEntry!));

                    activity?.SetTag("Old ServiceEntry", JsonSerializer.Serialize(oldServiceEntry));
                    activity?.SetTag("New ServiceEntry", JsonSerializer.Serialize(newServiceEntry));
                    activity?.SetTag("AutoMapped New ServiceEntry:", JsonSerializer.Serialize(mappedServiceResponse));

                    await _retryPipeline.ExecuteAsync(
                    async (token) =>
                    {
                        var existingServiceEntries = await _catalogClient.GetAllAsync().ConfigureAwait(false);
                        var entryfromCatalogApi = existingServiceEntries?.Items?.Where(x => x.Id!.Equals(serviceId, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();

                        activity?.SetTag("Catalog Api ServiceResponse:", JsonSerializer.Serialize(entryfromCatalogApi));

                        if (entryfromCatalogApi == null || !entryfromCatalogApi.Equals(mappedServiceResponse))
                        {
                            throw new InvalidDataException($"{serviceId} is not updated in Catalog service entries.");
                        }
                    },
                    CancellationToken.None).ConfigureAwait(false);

                    await _eventBus.PublishAsync(updateEvent).ConfigureAwait(false);
                    timer.Stop();
                    _metrics.RecordEvents(V1EventPublishStatus.Success, updateEvent);
                    _metrics.PublishEventProcessingTime(timer.Elapsed.TotalMilliseconds, updateEvent);
                }
                catch (Exception ex)
                {
                    activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                    _metrics.RecordEvents(V1EventPublishStatus.Failure, updateEvent);
                    activity?.AddException(ex);
                    _logger?.CatalogServicEntryUpdateEventError(serviceId, _region);
                }
            }
        }

        private static ServiceResponse MapServiceAvailability(ServiceResponse serviceResponse)
        {
            if (serviceResponse.Lifecycle == null || serviceResponse.Lifecycle.Trigger != V1Trigger.Catalog.ToString())
            {
                return serviceResponse;
            }

            // To determine whether updates have been made to the service availability, we need to ensure that the serviceResponse.Availability is not null.
            var defaultServiceAvailability = V1ServiceAvailability.GetDefault();
            var serviceAvailability = new ServiceAvailability()
            {
                Limit = serviceResponse.Availability?.Limit ?? defaultServiceAvailability.Limit,
                Enabled = serviceResponse.Availability?.Enabled ?? defaultServiceAvailability.Enabled,
            };

            serviceResponse.Availability = serviceAvailability;
            return serviceResponse;
        }
    }
}