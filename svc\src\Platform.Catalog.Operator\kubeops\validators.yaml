apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validators
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlCbFRDQ0FUdWdBd0lCQWdJSU54aXp1MmJhdnZNd0NnWUlLb1pJemowRUF3UXdQakVaTUJjR0ExVUVBd3dRDQpUM0JsY21GMGIzSWdVbTl2ZENCRFFURU1NQW9HQTFVRUJoTURSRVZXTVJNd0VRWURWUVFIREFwTGRXSmxjbTVsDQpkR1Z6TUI0WERUSTFNRGN3TXpBd01EQXdNRm9YRFRNd01EY3dNekF3TURBd01Gb3dQakVaTUJjR0ExVUVBd3dRDQpUM0JsY21GMGIzSWdVbTl2ZENCRFFURU1NQW9HQTFVRUJoTURSRVZXTVJNd0VRWURWUVFIREFwTGRXSmxjbTVsDQpkR1Z6TUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFVGZGMVhTU0F1aHNybWJhekk5ZEkyR1h0DQo5a2taUjByVDdJbWM1TFdKKzMyU0NQTUl0RHFDTGUrVGpsRFpZSUtZUG8rMUpVR2FDK3psWlV6V29aTkRKcU1qDQpNQ0V3RHdZRFZSMFRBUUgvQkFVd0F3RUIvekFPQmdOVkhROEJBZjhFQkFNQ0FTWXdDZ1lJS29aSXpqMEVBd1FEDQpTQUF3UlFJZ0JvdHMxbm4vMksxSGZvQ1BIK0dJM2Y1bTJTOFBkSEs1N1QxVWJEVWNvak1DSVFEaUdtdXc0WTNODQpuM1BWTjRUVjl3UlovUDRpN0R4UDU0VlcvQy9scnZxZjV3PT0NCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0NCg==
    service:
      name: operator
      path: /validate/v1serviceentry
  matchPolicy: Exact
  name: validate.serviceentry.servicecatalog.aveva.com.v1
  rules:
  - apiGroups:
    - servicecatalog.aveva.com
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    - DELETE
    resources:
    - serviceentries
  sideEffects: None