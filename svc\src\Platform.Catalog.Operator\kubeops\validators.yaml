apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validators
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlCbFRDQ0FUdWdBd0lCQWdJSU14U0luT3VsaVRvd0NnWUlLb1pJemowRUF3UXdQakVaTUJjR0ExVUVBd3dRDQpUM0JsY21GMGIzSWdVbTl2ZENCRFFURU1NQW9HQTFVRUJoTURSRVZXTVJNd0VRWURWUVFIREFwTGRXSmxjbTVsDQpkR1Z6TUI0WERUSTFNRFl5TlRBd01EQXdNRm9YRFRNd01EWXlOVEF3TURBd01Gb3dQakVaTUJjR0ExVUVBd3dRDQpUM0JsY21GMGIzSWdVbTl2ZENCRFFURU1NQW9HQTFVRUJoTURSRVZXTVJNd0VRWURWUVFIREFwTGRXSmxjbTVsDQpkR1Z6TUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFanRoV3d4S0hlbUYrdU9vR1FOYWJDUkY2DQpZYkVpNGJ4NDArT2tQU1BHM1BlNjIyQ1dVcDUybGdDMGRMVEk4WXhpZTByVU44YXpVMHhEekRQRzNwY3I4YU1qDQpNQ0V3RHdZRFZSMFRBUUgvQkFVd0F3RUIvekFPQmdOVkhROEJBZjhFQkFNQ0FTWXdDZ1lJS29aSXpqMEVBd1FEDQpTQUF3UlFJaEFLVnF6YUFSVGRQUEJnc2E0U1ZnTDlPbTJRSDI2Y1NSbjM0VTN4K3ZjY3JJQWlCNEoxVForR1dVDQo1d0YrbjUwaFAvNzUxQkpNc0hEYlYyK2NleFJRcEFIS1lRPT0NCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0NCg==
    service:
      name: operator
      path: /validate/v1serviceentry
  matchPolicy: Exact
  name: validate.serviceentry.servicecatalog.aveva.com.v1
  rules:
  - apiGroups:
    - servicecatalog.aveva.com
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    - DELETE
    resources:
    - serviceentries
  sideEffects: None