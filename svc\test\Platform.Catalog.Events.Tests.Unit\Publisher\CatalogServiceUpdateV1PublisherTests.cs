﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Events.Publisher;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using Microsoft.Extensions.Logging;
using Moq;
using Polly;
using Polly.Retry;
using Shouldly;

namespace Aveva.Platform.Catalog.Events.Tests.Unit.Publisher
{
    [Trait("Category", "App")]
    [Trait("Category", "Unit")]
    [Trait("Category", "App.Unit")]
    [Trait("Tag", "Publisher")]
    public class CatalogServiceUpdateV1PublisherTests
    {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
        #region Test Cases
        private static readonly RetryStrategyOptions _retryStrategyOptions = new RetryStrategyOptions()
        {
            BackoffType = DelayBackoffType.Exponential,
            MaxRetryAttempts = 1,
            Delay = TimeSpan.FromMilliseconds(100),
        };

        private static readonly ResiliencePipeline _retryPipeline = new ResiliencePipelineBuilder()
            .AddRetry(_retryStrategyOptions)
            .Build();
        private Mock<IEventBus> _eventBus = new Mock<IEventBus>();
        private Mock<ICatalogClient> _catalogClient = new Mock<ICatalogClient>();
        private Mock<ILogger> _logger = new Mock<ILogger>();
        private Mock<ITypeMappingService> _mapper = new Mock<ITypeMappingService>();
        private Mock<EventMetrics> _metrics = new Mock<EventMetrics>();

        [Fact]
        public void CatalogServiceUpdateV1Publisher_Initialization_NullException()
        {
            // Act & Assert
            Should.Throw<ArgumentNullException>(() =>
            {
                var eventPublisher = new CatalogServiceUpdateV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "Us", region: "us", retryPipeline: null, _mapper.Object, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentNullException>(() =>
            {
                var eventPublisher = new CatalogServiceUpdateV1Publisher(null, _catalogClient.Object, geography: "Us", region: "us", retryPipeline: _retryPipeline, _mapper.Object, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentNullException>(() =>
            {
                var eventPublisher = new CatalogServiceUpdateV1Publisher(_eventBus.Object, null, geography: "Us", region: "us", retryPipeline: _retryPipeline, _mapper.Object, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentException>(() =>
            {
                var eventPublisher = new CatalogServiceUpdateV1Publisher(_eventBus.Object, _catalogClient.Object, geography: string.Empty, region: "us", retryPipeline: _retryPipeline, _mapper.Object, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentException>(() =>
            {
                var eventPublisher = new CatalogServiceUpdateV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: string.Empty, retryPipeline: _retryPipeline, _mapper.Object, _metrics.Object, _logger.Object);
            });

            Should.NotThrow(() =>
            {
                var eventPublisher = new CatalogServiceUpdateV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "us", retryPipeline: _retryPipeline, _mapper.Object, _metrics.Object, null);
            });
        }

        [Fact]
        public async Task CatalogServiceUpdateV1Publisher_PublishEventAsync_Succeeds()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var mappedServiceResponse = new ServiceResponse()
            {
                Id = "updatedServiceId",
                HostingType = "Environment",
                Category = Category.Data,
            };
            var oldServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
            };
            var newServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
            };

            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "updatedServiceId", Category = Category.Data, HostingType = "Environment", } },
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse)!);
            _mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>()))
                   .Returns(mappedServiceResponse);

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: _retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(oldServiceEntry, newServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _eventBus.Verify(
                x => x.PublishAsync(
                It.Is<CatalogServiceUpdateV1>(e =>
                    e.OldServiceEntry != null &&
                    e.NewServiceEntry != null)),
                Times.Once);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1Publisher_PublishEventAsync_WithNullAvailability_Succeeds()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var mappedServiceResponse = new ServiceResponse()
            {
                Id = "updatedServiceId",
                HostingType = "Environment",
                Category = Category.Data,
                Lifecycle = new Lifecycle
                {
                    InstanceMode = "Shared",
                    Protocol = "IntegrationEvent",
                    ProviderId = "providerId",
                    Trigger = "Catalog",
                },
            };
            var oldServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
            };
            var newServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Shared,
                    Protocol = V1IntegrationProtocol.IntegrationEvent,
                    ProviderId = "providerId",
                    Trigger = V1Trigger.Catalog,
                },
            };

            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>()
                {
                    new ServiceResponse()
                    {
                        Id = "updatedServiceId",
                        Category = Category.Data,
                        HostingType = "Environment",
                        Lifecycle = new Lifecycle()
                        {
                            InstanceMode = "Shared",
                            Protocol = "IntegrationEvent",
                            ProviderId = "providerId",
                            Trigger = "Catalog",
                        },
                        Availability = new ServiceAvailability()
                        {
                            Enabled = true,
                            Limit = 10,
                        },
                    },
                },
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse)!);
            _mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>()))
                   .Returns(mappedServiceResponse);

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: _retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(oldServiceEntry, newServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _eventBus.Verify(
                x => x.PublishAsync(
                It.Is<CatalogServiceUpdateV1>(e =>
                    e.OldServiceEntry != null &&
                    e.NewServiceEntry != null)),
                Times.Once);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1Publisher_PublishEventAsync_WithPartialAvailability_Succeeds()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var mappedServiceResponse = new ServiceResponse()
            {
                Id = "updatedServiceId",
                HostingType = "Environment",
                Category = Category.Data,
                Lifecycle = new Lifecycle
                {
                    InstanceMode = "Shared",
                    Protocol = "IntegrationEvent",
                    ProviderId = "providerId",
                    Trigger = "Catalog",
                },
                Availability = new ServiceAvailability()
                {
                    Limit = 4,
                    Enabled = false,
                },
            };
            var oldServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
            };
            var newServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Shared,
                    Protocol = V1IntegrationProtocol.IntegrationEvent,
                    ProviderId = "providerId",
                    Trigger = V1Trigger.Catalog,
                },
                Availability = new V1ServiceAvailability()
                {
                    Limit = 4,
                    Enabled = false,
                },
            };

            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>()
                {
                    new ServiceResponse()
                    {
                        Id = "updatedServiceId",
                        Category = Category.Data,
                        HostingType = "Environment",
                        Lifecycle = new Lifecycle()
                        {
                            InstanceMode = "Shared",
                            Protocol = "IntegrationEvent",
                            ProviderId = "providerId",
                            Trigger = "Catalog",
                        },
                        Availability = new ServiceAvailability()
                        {
                            Enabled = false,
                            Limit = 4,
                        },
                    },
                },
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse)!);
            _mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>()))
                   .Returns(mappedServiceResponse);

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: _retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(oldServiceEntry, newServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _eventBus.Verify(
                x => x.PublishAsync(
                It.Is<CatalogServiceUpdateV1>(e =>
                    e.OldServiceEntry != null &&
                    e.NewServiceEntry != null)),
                Times.Once);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1Publisher_PublishEventAsync_ThrowsOnNullOldEntry()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var newServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
            };

            // Act
            await Should.ThrowAsync<ArgumentNullException>(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: _retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(null, newServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceUpdateV1>()), Times.Never);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1Publisher_PublishEventAsync_ThrowsOnNullNewEntry()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var oldServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
            };

            // Act
            await Should.ThrowAsync<ArgumentNullException>(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: _retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(oldServiceEntry, null)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceUpdateV1>()), Times.Never);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1PublisherTests_PublishEventAsync_WaitForUpdatingService_IntegrationEvent()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var originalDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.One,
                        Colocated = true,
                        Type = DependencyType.Optional,
                    }
                },
            };

            var updatedDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.Many,
                        Colocated = false,
                        Type = DependencyType.Required,
                    }
                },
            };

            var webhookReceivedDependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "first",
                    new V1CatalogDataDependency()
                    {
                        Cardinality = V1CatalogDataDependencyCardinality.Many,
                        Colocated = false,
                        Type = V1CatalogDataDependencyType.Required,
                    }
                },
            };

            List<ServiceResponse> lstServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "modifiedId",
                    HostingType = V1HostingType.External.ToString(),
                    Description = "Description",
                    DisplayName = "Name",
                    Lifecycle = new Lifecycle
                        {
                            InstanceMode = V1InstanceMode.Isolated.ToString(),
                            Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                            ProviderId = "providerId",
                            Trigger = V1Trigger.Catalog.ToString(),
                        },
                    Dependencies = originalDependencies,
                },
            };
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = lstServiceResponse,
            };

            List<ServiceResponse> lstUpdatedServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "modifiedId",
                    Description = "updatedDescription",
                    Category = Category.Data,
                    HostingType = V1HostingType.Environment.ToString(),
                    DisplayName = "Nameupdated",
                    Lifecycle = new Lifecycle
                    {
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                        Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                        ProviderId = "updatedproviderId",
                        Trigger = V1Trigger.Account.ToString(),
                    },
                    Dependencies = updatedDependencies,
                    Availability = new ServiceAvailability()
                    {
                        Enabled = true,
                        Limit = 4,
                    },
                    Applications = [new Application
                        {
                            Name = "Name",
                            Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
                        }
                    ],
                    Geographies = new List<Geography> { new() { Id = "eu" } },
                },
            };
            ServiceCollectionResponse? updatedResponse = new ServiceCollectionResponse()
            {
                Items = lstUpdatedServiceResponse,
            };

            var originalServiceEntry = new V1ServiceEntry()
            {
                Id = "modifiedId",
                Description = "Description",
                HostingType = V1HostingType.External,
                DisplayName = "Name",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    Protocol = V1IntegrationProtocol.IntegrationEvent,
                    ProviderId = "providerId",
                    Trigger = V1Trigger.Catalog,
                },
                Dependencies = webhookReceivedDependencies,
            };

            var updatedServiceEntry = new V1ServiceEntry()
            {
                Id = "modifiedId",
                Description = "updatedDescription",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                DisplayName = "Nameupdated",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Shared,
                    Protocol = V1IntegrationProtocol.IntegrationEvent,
                    ProviderId = "updatedproviderId",
                    Trigger = V1Trigger.Account,
                },
                Dependencies = webhookReceivedDependencies,
            };

            var webhookReceivedResponseDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.Many,
                        Colocated = false,
                        Type = DependencyType.Required,
                    }
                },
            };

            var updatedServiceResponse = new ServiceResponse()
            {
                Id = "modifiedId",
                Description = "updatedDescription",
                Category = Category.Data,
                HostingType = "Environment",
                DisplayName = "Nameupdated",
                Lifecycle = new Lifecycle
                {
                    InstanceMode = "Shared",
                    Protocol = "IntegrationEvent",
                    ProviderId = "updatedproviderId",
                    Trigger = "Account",
                },
                Dependencies = webhookReceivedResponseDependencies,
                Availability = new ServiceAvailability()
                {
                    Enabled = true,
                    Limit = 4,
                },
                Applications = [new Application
                        {
                            Name = "Name",
                            Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
                        }
                    ],
                Geographies = new List<Geography> { new() { Id = "eu" } },
            };

            _mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>())).Returns(updatedServiceResponse);

            _catalogClient.SetupSequence(x => x.GetAllAsync())
                .Returns(Task.FromResult(catalogResponse)!)
                .Returns(Task.FromResult(catalogResponse)!)
                .Returns(Task.FromResult(updatedResponse)!);

            RetryStrategyOptions retryStrategyOptions = new RetryStrategyOptions()
            {
                BackoffType = DelayBackoffType.Exponential,
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromMilliseconds(100),
            };

            ResiliencePipeline retryPipeline = new ResiliencePipelineBuilder()
                .AddRetry(retryStrategyOptions)
                .Build();

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(updatedServiceEntry, originalServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _mapper.Invocations.Count.ShouldBe(1);
            _catalogClient.Invocations.Count.ShouldBe(3);
            _eventBus.Verify(
                x => x.PublishAsync(
                It.Is<CatalogServiceUpdateV1>(e =>
                    e.NewServiceEntry != null &&
                    e.OldServiceEntry != null)),
                Times.Once);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1PublisherTests_PublishEventAsync_WaitForUpdatingService_LegacyProtocolOptions()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var originalDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.One,
                        Colocated = true,
                        Type = DependencyType.Optional,
                    }
                },
            };

            var updatedDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.Many,
                        Colocated = false,
                        Type = DependencyType.Required,
                    }
                },
            };

            var webhookReceivedDependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "first",
                    new V1CatalogDataDependency()
                    {
                        Cardinality = V1CatalogDataDependencyCardinality.Many,
                        Colocated = false,
                        Type = V1CatalogDataDependencyType.Required,
                    }
                },
            };

            List<ServiceResponse> lstServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "modifiedId",
                    HostingType = V1HostingType.External.ToString(),
                    Description = "Description",
                    DisplayName = "Name",
                    Lifecycle = new Lifecycle
                        {
                            InstanceMode = V1InstanceMode.Isolated.ToString(),
                            Protocol = V1IntegrationProtocol.Legacy.ToString(),
                            ProviderId = "providerId",
                            Trigger = V1Trigger.Catalog.ToString(),
                            ProtocolOptions = new LegacyProtocolOptions
                            {
                               SolutionDefinition = "solutionDefinition",
                               Mappings = new LegacyProtocolMappings(),
                            },
                        },
                    Dependencies = originalDependencies,
                },
            };
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = lstServiceResponse,
            };

            List<ServiceResponse> lstUpdatedServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "modifiedId",
                    Description = "updatedDescription",
                    Category = Category.Data,
                    HostingType = V1HostingType.Environment.ToString(),
                    DisplayName = "Nameupdated",
                    Lifecycle = new Lifecycle
                    {
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                        ProviderId = "updatedproviderId",
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.Legacy.ToString(),
                        ProtocolOptions = new LegacyProtocolOptions
                        {
                            SolutionDefinition = "solutionDefinition-modified",
                            Mappings = new LegacyProtocolMappings(),
                        },
                    },
                    Dependencies = updatedDependencies,
                    Availability = new ServiceAvailability()
                    {
                        Limit = 4,
                        Visible = false,
                    },
                    Applications = [new Application
                        {
                            Name = "Name",
                            Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
                        }
                    ],
                    Geographies = new List<Geography> { new() { Id = "eu" } },
                },
            };
            ServiceCollectionResponse? updatedResponse = new ServiceCollectionResponse()
            {
                Items = lstUpdatedServiceResponse,
            };

            var originalServiceEntry = new V1ServiceEntry()
            {
                Id = "modifiedId",
                Description = "Description",
                HostingType = V1HostingType.External,
                DisplayName = "Name",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    Protocol = V1IntegrationProtocol.Legacy,
                    ProtocolOptions = new V1ProtocolOptions
                    {
                        SolutionDefinition = "solutionDefinition",
                        Mappings = new V1LegacyProtocolMappings(),
                    },
                    ProviderId = "providerId",
                    Trigger = V1Trigger.Catalog,
                },
                Dependencies = webhookReceivedDependencies,
            };

            var updatedServiceEntry = new V1ServiceEntry()
            {
                Id = "modifiedId",
                Description = "updatedDescription",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                DisplayName = "Nameupdated",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Shared,
                    Protocol = V1IntegrationProtocol.Legacy,
                    ProtocolOptions = new V1ProtocolOptions
                    {
                        SolutionDefinition = "solutionDefinition-modified",
                        Mappings = new V1LegacyProtocolMappings(),
                    },
                    ProviderId = "updatedproviderId",
                    Trigger = V1Trigger.Account,
                },
                Dependencies = webhookReceivedDependencies,
            };

            var webhookReceivedResponseDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.Many,
                        Colocated = false,
                        Type = DependencyType.Required,
                    }
                },
            };

            var updatedServiceResponse = new ServiceResponse()
            {
                Id = "modifiedId",
                Description = "updatedDescription",
                Category = Category.Data,
                HostingType = "Environment",
                DisplayName = "Nameupdated",
                Lifecycle = new Lifecycle
                {
                    InstanceMode = "Shared",
                    Protocol = "Legacy",
                    ProviderId = "updatedproviderId",
                    Trigger = "Account",
                    ProtocolOptions = new LegacyProtocolOptions
                    {
                        SolutionDefinition = "solutionDefinition-modified",
                        Mappings = new LegacyProtocolMappings(),
                    },
                },
                Dependencies = webhookReceivedResponseDependencies,
                Availability = new ServiceAvailability()
                {
                    Limit = 4,
                    Visible = false,
                },
                Applications = [new Application
                        {
                            Name = "Name",
                            Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
                        }
                    ],
                Geographies = new List<Geography> { new() { Id = "eu" } },
            };

            _mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>())).Returns(updatedServiceResponse);

            _catalogClient.SetupSequence(x => x.GetAllAsync())
                .Returns(Task.FromResult(catalogResponse)!)
                .Returns(Task.FromResult(catalogResponse)!)
                .Returns(Task.FromResult(updatedResponse)!);

            RetryStrategyOptions retryStrategyOptions = new RetryStrategyOptions()
            {
                BackoffType = DelayBackoffType.Exponential,
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromMilliseconds(100),
            };

            ResiliencePipeline retryPipeline = new ResiliencePipelineBuilder()
                .AddRetry(retryStrategyOptions)
                .Build();

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(updatedServiceEntry, originalServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _mapper.Invocations.Count.ShouldBe(1);
            _catalogClient.Invocations.Count.ShouldBe(3);
            _eventBus.Verify(
                x => x.PublishAsync(
                It.Is<CatalogServiceUpdateV1>(e =>
                    e.NewServiceEntry != null &&
                    e.OldServiceEntry != null)),
                Times.Once);
        }

        [Fact]
        public async Task CatalogServiceUpdateV1PublisherTests_PublishEventAsync_WaitForUpdatingService_WebhookProtocolOptions()
        {
            // Arrange
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var originalDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.One,
                        Colocated = true,
                        Type = DependencyType.Optional,
                    }
                },
            };

            var updatedDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.Many,
                        Colocated = false,
                        Type = DependencyType.Required,
                    }
                },
            };

            var webhookReceivedDependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "first",
                    new V1CatalogDataDependency()
                    {
                        Cardinality = V1CatalogDataDependencyCardinality.Many,
                        Colocated = false,
                        Type = V1CatalogDataDependencyType.Required,
                    }
                },
            };

            List<ServiceResponse> lstServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "modifiedId",
                    HostingType = V1HostingType.External.ToString(),
                    Description = "Description",
                    DisplayName = "Name",
                    Lifecycle = new Lifecycle
                        {
                            InstanceMode = V1InstanceMode.Isolated.ToString(),
                            ProviderId = "providerId",
                            Trigger = V1Trigger.Catalog.ToString(),
                            Protocol = V1IntegrationProtocol.Webhook.ToString(),
                            ProtocolOptions = new WebhookProtocolOptions
                            {
                                WebhookUri = new Uri("http://test.com"),
                            },
                        },
                    Dependencies = originalDependencies,
                },
            };
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = lstServiceResponse,
            };

            List<ServiceResponse> lstUpdatedServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "modifiedId",
                    Description = "updatedDescription",
                    Category = Category.Data,
                    HostingType = V1HostingType.Environment.ToString(),
                    DisplayName = "Nameupdated",
                    Lifecycle = new Lifecycle
                    {
                        InstanceMode = V1InstanceMode.Shared.ToString(),
                        ProviderId = "updatedproviderId",
                        Trigger = V1Trigger.Account.ToString(),
                        Protocol = V1IntegrationProtocol.Webhook.ToString(),
                        ProtocolOptions = new WebhookProtocolOptions
                        {
                            WebhookUri = new Uri("http://test-modified.com"),
                        },
                    },
                    Dependencies = updatedDependencies,
                    Availability = new ServiceAvailability()
                    {
                        Limit = 4,
                        Visible = false,
                    },
                    Applications = [new Application
                        {
                            Name = "Name",
                            Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
                        }
                    ],
                    Geographies = new List<Geography> { new() { Id = "eu" } },
                },
            };
            ServiceCollectionResponse? updatedResponse = new ServiceCollectionResponse()
            {
                Items = lstUpdatedServiceResponse,
            };

            var originalServiceEntry = new V1ServiceEntry()
            {
                Id = "modifiedId",
                Description = "Description",
                HostingType = V1HostingType.External,
                DisplayName = "Name",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    Protocol = V1IntegrationProtocol.Webhook,
                    ProtocolOptions = new V1ProtocolOptions
                    {
                        WebhookUri = new Uri("http://test.com"),
                    },
                    ProviderId = "providerId",
                    Trigger = V1Trigger.Catalog,
                },
                Dependencies = webhookReceivedDependencies,
            };

            var updatedServiceEntry = new V1ServiceEntry()
            {
                Id = "modifiedId",
                Description = "updatedDescription",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                DisplayName = "Nameupdated",
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Shared,
                    Protocol = V1IntegrationProtocol.Webhook,
                    ProtocolOptions = new V1ProtocolOptions
                    {
                        WebhookUri = new Uri("http://test-modified.com"),
                    },
                    ProviderId = "updatedproviderId",
                    Trigger = V1Trigger.Account,
                },
                Dependencies = webhookReceivedDependencies,
            };

            var webhookReceivedResponseDependencies = new Dictionary<string, Dependency>
            {
                {
                    "first",
                    new Dependency()
                    {
                        Cardinality = DependencyCardinality.Many,
                        Colocated = false,
                        Type = DependencyType.Required,
                    }
                },
            };

            var updatedServiceResponse = new ServiceResponse()
            {
                Id = "modifiedId",
                Description = "updatedDescription",
                Category = Category.Data,
                HostingType = "Environment",
                DisplayName = "Nameupdated",
                Lifecycle = new Lifecycle
                {
                    InstanceMode = "Shared",
                    Protocol = "Webhook",
                    ProviderId = "updatedproviderId",
                    Trigger = "Account",
                    ProtocolOptions = new WebhookProtocolOptions
                    {
                       WebhookUri = new Uri("http://test-modified.com"),
                    },
                },
                Dependencies = webhookReceivedResponseDependencies,
                Availability = new ServiceAvailability()
                {
                    Limit = 4,
                    Visible = false,
                },
                Applications = [new Application
                        {
                            Name = "Name",
                            Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
                        }
                    ],
                Geographies = new List<Geography> { new() { Id = "eu" } },
            };

            _mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>())).Returns(updatedServiceResponse);

            _catalogClient.SetupSequence(x => x.GetAllAsync())
                .Returns(Task.FromResult(catalogResponse)!)
                .Returns(Task.FromResult(catalogResponse)!)
                .Returns(Task.FromResult(updatedResponse)!);

            RetryStrategyOptions retryStrategyOptions = new RetryStrategyOptions()
            {
                BackoffType = DelayBackoffType.Exponential,
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromMilliseconds(100),
            };

            ResiliencePipeline retryPipeline = new ResiliencePipelineBuilder()
                .AddRetry(retryStrategyOptions)
                .Build();

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(
                    _eventBus.Object,
                    _catalogClient.Object,
                    geography: "us",
                    region: "us",
                    retryPipeline: retryPipeline,
                    _mapper.Object,
                    _metrics.Object);

                await serviceEntryUpdatePublisher!.PublishEventAsync(updatedServiceEntry, originalServiceEntry)
                    .ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _mapper.Invocations.Count.ShouldBe(1);
            _catalogClient.Invocations.Count.ShouldBe(3);
            _eventBus.Verify(
                x => x.PublishAsync(
                It.Is<CatalogServiceUpdateV1>(e =>
                    e.NewServiceEntry != null &&
                    e.OldServiceEntry != null)),
                Times.Once);
        }
        #endregion
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
    }
}